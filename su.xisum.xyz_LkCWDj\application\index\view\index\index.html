<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字资产管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .feature {
            padding: 20px;
            border-radius: 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 15px;
        }
        
        .feature h3 {
            color: #333;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 40px;
        }
        
        .btn {
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
            color: #999;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">💎</div>
        <h1>数字资产管理平台</h1>
        <p class="subtitle">
            安全、高效的数字资产管理解决方案<br>
            支持多链钱包、挖矿管理、Web3登录等完整功能
        </p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🔐</div>
                <h3>Web3钱包登录</h3>
                <p>支持MetaMask等主流钱包，安全便捷的Web3身份验证</p>
            </div>
            <div class="feature">
                <div class="feature-icon">⛏️</div>
                <h3>挖矿管理</h3>
                <p>智能矿机管理，实时收益计算，全面的挖矿数据分析</p>
            </div>
            <div class="feature">
                <div class="feature-icon">💰</div>
                <h3>资产管理</h3>
                <p>多链资产管理，充值提现，完整的资金流水记录</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h3>实时监控</h3>
                <p>系统监控告警，安全事件检测，全方位的运营数据</p>
            </div>
        </div>
        
        <div class="buttons">
            <a href="/admin" class="btn btn-primary">
                🚀 进入管理后台
            </a>
            <a href="/admin/web3monitor/dashboard" class="btn btn-secondary">
                📈 查看监控面板
            </a>
        </div>
        
        <div class="footer">
            <p>© 2024 数字资产管理平台 | 基于ThinkPHP 5.1 + Web3技术构建</p>
        </div>
    </div>
</body>
</html>
