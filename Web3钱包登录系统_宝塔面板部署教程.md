# 数字资产管理平台 - 宝塔面板完整部署教程

## 📋 教程概述

本教程将指导您在宝塔面板环境下完整部署数字资产管理平台，这是一个基于ThinkPHP 5.1框架开发的综合性数字资产管理系统，集成了Web3钱包登录、挖矿管理、资产管理等完整功能。

### 🎯 部署目标
- ✅ 完整的用户管理和权限系统
- ✅ 数字钱包管理（地址管理、助记词管理）
- ✅ 挖矿系统（矿机管理、收益计算、记录追踪）
- ✅ 资金管理（充值、提现、审核流程）
- ✅ Web3钱包登录功能
- ✅ 多链支持（Ethereum、BSC、Polygon、Arbitrum、Optimism）
- ✅ 实时监控告警系统
- ✅ 完整的日志审计系统
- ✅ 自动化运维工具
- ✅ 安全防护和性能优化

## 🚀 快速部署（推荐）

### 方式一：自动化脚本部署（5分钟）

```bash
# 1. 下载项目文件到服务器
cd /root
wget https://your-domain.com/digital-asset-platform.zip
unzip digital-asset-platform.zip
cd su.xisum.xyz_LkCWDj

# 2. 运行自动部署脚本
chmod +x deploy_baota.sh
./deploy_baota.sh

# 3. 按提示输入配置信息
# - 域名：your-domain.com
# - 数据库名：digital_asset_db
# - 数据库用户：asset_user
# - 数据库密码：（自动生成或手动输入）
# - 管理员邮箱：<EMAIL>
# - 启用SSL：y

# 4. 等待部署完成
```

**部署完成后访问：**
- 网站首页：`https://your-domain.com`
- 管理后台：`https://your-domain.com/admin`
- 用户管理：`https://your-domain.com/admin/user`
- 钱包管理：`https://your-domain.com/admin/casies`
- 挖矿管理：`https://your-domain.com/admin/mining`
- 资金管理：`https://your-domain.com/admin/recharge`
- Web3监控：`https://your-domain.com/admin/web3monitor/dashboard`

### 方式二：手动部署（详细步骤）

## 🛠️ 环境要求

### 服务器配置
| 项目 | 最低要求 | 推荐配置 |
|------|----------|----------|
| CPU | 2核 | 4核+ |
| 内存 | 2GB | 4GB+ |
| 硬盘 | 20GB | 50GB+ |
| 带宽 | 5Mbps | 10Mbps+ |

### 软件环境
- **宝塔面板**：7.7.0+
- **PHP**：8.0+ （必须）
- **MySQL**：8.0+ （必须）
- **Nginx**：1.20+ （必须）
- **Redis**：6.0+ （可选，用于缓存）

## 📦 第一步：安装宝塔面板

### 1.1 一键安装宝塔面板

**CentOS 7/8：**
```bash
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh ed8484bec
```

**Ubuntu/Debian：**
```bash
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh ed8484bec
```

### 1.2 登录宝塔面板
1. 安装完成后记录面板地址、用户名和密码
2. 浏览器访问：`http://your-server-ip:8888`
3. 使用安装时显示的账号密码登录

### 1.3 安装软件环境
在宝塔面板 → 软件商店中一键安装：

**必装软件（LNMP环境）：**
- ✅ **Nginx** 1.20+
- ✅ **MySQL** 8.0+
- ✅ **PHP** 8.0+
- ✅ **phpMyAdmin** 5.0+

**推荐软件：**
- 🔧 **Redis** 6.0+ （缓存加速）
- 📊 **Supervisor** （进程管理）
- 🛡️ **宝塔防火墙** （安全防护）

## 🗄️ 第二步：数据库配置

### 2.1 创建数据库
1. 进入宝塔面板 → 数据库
2. 点击"添加数据库"
3. 填写配置：
   ```
   数据库名：digital_asset_db
   用户名：asset_user
   密码：[生成强密码]
   访问权限：本地服务器
   字符集：utf8mb4
   ```

### 2.2 导入数据库结构
**方法一：phpMyAdmin导入**
1. 点击数据库名进入phpMyAdmin
2. 选择"导入"选项卡
3. 依次上传并执行以下SQL文件：
   - `web3_migration.sql` - Web3功能相关表
   - `update.sql` - 业务功能相关表

**方法二：命令行导入**
```bash
cd /www/wwwroot/your-domain.com
# 导入Web3功能表结构
mysql -u asset_user -p digital_asset_db < web3_migration.sql
# 导入业务功能表结构
mysql -u asset_user -p digital_asset_db < update.sql
```

### 2.3 验证数据库表
确认以下核心表已创建：

**用户和权限相关：**
- ✅ `tbl_users` - 用户表（已扩展Web3字段）
- ✅ `aw_log` - 操作日志表

**数字资产相关：**
- ✅ `aw_casies` - 钱包地址管理表
- ✅ `aw_mnemonics` - 助记词管理表
- ✅ `aw_fish` - 用户资产表

**挖矿系统相关：**
- ✅ `aw_mining` - 矿机配置表
- ✅ `aw_mining_record` - 挖矿记录表
- ✅ `aw_mining_income` - 挖矿收益表
- ✅ `aw_mining_machines` - 矿机管理表

**资金管理相关：**
- ✅ `recharge` - 充值记录表
- ✅ `aw_withdraw` - 提现记录表

**Web3功能相关：**
- ✅ `web3_login_logs` - Web3登录日志表
- ✅ `wallet_nonces` - 钱包nonce缓存表

## 🌐 第三步：网站配置

### 3.1 创建网站
1. 宝塔面板 → 网站 → 添加站点
2. 配置信息：
   ```
   域名：your-domain.com
   根目录：/www/wwwroot/your-domain.com
   PHP版本：8.0
   数据库：选择已创建的数据库
   ```

### 3.2 上传项目文件
**方法一：宝塔文件管理器**
1. 进入网站根目录
2. 上传项目压缩包
3. 解压到当前目录

**方法二：命令行上传**
```bash
# 使用scp上传
scp -r ./web3-project/* root@your-server:/www/wwwroot/your-domain.com/

# 或使用git克隆
cd /www/wwwroot/your-domain.com
git clone https://github.com/your-repo/web3-wallet-system.git .
```

### 3.3 设置运行目录
1. 网站设置 → 网站目录
2. **运行目录**：设置为 `/public`
3. **防跨站攻击**：关闭
4. 保存设置

### 3.4 配置URL重写
网站设置 → 伪静态，添加ThinkPHP规则：
```nginx
location / {
    if (!-e $request_filename) {
        rewrite ^(.*)$ /index.php?s=/$1 last;
        break;
    }
}
```

## 🔧 第四步：PHP环境配置

### 4.1 安装PHP扩展
PHP设置 → 安装扩展，确保安装：
- ✅ **mysqli** - MySQL数据库连接
- ✅ **pdo_mysql** - PDO数据库驱动
- ✅ **curl** - HTTP请求支持
- ✅ **openssl** - 加密支持（Web3必需）
- ✅ **json** - JSON数据处理
- ✅ **mbstring** - 多字节字符串
- ✅ **gd** - 图像处理
- 🔧 **redis** - Redis缓存（可选）

### 4.2 优化PHP配置
PHP设置 → 配置修改：
```ini
# 基础性能配置
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
post_max_size = 50M
upload_max_filesize = 50M

# 错误处理（生产环境）
display_errors = Off
log_errors = On
error_log = /www/wwwroot/your-domain.com/runtime/log/php_error.log

# 会话安全
session.cookie_secure = 1
session.cookie_httponly = 1
session.gc_maxlifetime = 7200

# 时区设置
date.timezone = Asia/Shanghai
```

### 4.3 设置目录权限
```bash
# 设置项目目录权限
chown -R www:www /www/wwwroot/your-domain.com
chmod -R 755 /www/wwwroot/your-domain.com

# 设置运行时目录权限（重要）
chmod -R 777 /www/wwwroot/your-domain.com/runtime

# 设置敏感文件权限
chmod 600 /www/wwwroot/your-domain.com/application/database.php
```

## ⚙️ 第五步：项目配置

### 5.1 数据库配置
编辑 `application/database.php`：
```php
<?php
return [
    'type'            => 'mysql',
    'hostname'        => '127.0.0.1',
    'database'        => 'digital_asset_db',
    'username'        => 'asset_user',
    'password'        => 'your_database_password',
    'hostport'        => '3306',
    'charset'         => 'utf8mb4',
    'prefix'          => '',
    'debug'           => false,
    'deploy'          => 0,
    'rw_separate'     => false,
    'master_num'      => 1,
    'slave_no'        => '',
    'fields_strict'   => true,
    'resultset_type'  => 'array',
    'auto_timestamp'  => false,
    'datetime_format' => 'Y-m-d H:i:s',
    'sql_explain'     => false,
];
```

### 5.2 应用配置
编辑 `application/config.php`：
```php
<?php
return [
    'app_debug'               => false,  // 生产环境关闭
    'app_trace'               => false,  // 关闭页面Trace
    'url_route_on'            => true,   // 开启路由
    'url_route_must'          => false,
    'url_html_suffix'         => '',
    'default_timezone'        => 'Asia/Shanghai',
    // 其他配置保持默认
];
```

### 5.3 系统监控配置
编辑 `application/extra/web3_monitor.php`：
```php
<?php
return [
    'enabled' => true,
    'check_interval' => 300,

    'notifications' => [
        'email' => [
            'enabled' => true,
            'smtp_host' => 'smtp.exmail.qq.com',  // 腾讯企业邮箱
            'smtp_port' => 587,
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'your_email_password',
            'from_email' => '<EMAIL>',
            'from_name' => '数字资产平台监控系统',
            'to_emails' => ['<EMAIL>'],
            'alert_levels' => ['critical', 'error']
        ]
    ],

    'thresholds' => [
        // Web3相关监控
        'failed_login_rate' => 0.3,
        'rapid_login_count' => 20,
        'suspicious_ip_count' => 10,
        'invalid_signature_rate' => 0.2,
        'expired_nonce_count' => 1000,

        // 业务系统监控
        'mining_failure_rate' => 0.1,
        'withdraw_pending_count' => 50,
        'recharge_failure_rate' => 0.2,
        'user_login_failure_rate' => 0.3,
        'log_table_size' => 100000
    ]
];
```

## 🔒 第六步：SSL证书配置

### 6.1 申请免费SSL证书
1. 网站设置 → SSL
2. 选择"Let's Encrypt"
3. 填写邮箱地址
4. 点击申请证书
5. 等待申请完成

### 6.2 强制HTTPS
1. 开启"强制HTTPS"
2. 设置HSTS安全头
3. 验证HTTPS访问正常

**⚠️ 重要：Web3钱包连接必须在HTTPS环境下工作！**

## ⏰ 第七步：定时任务配置

### 7.1 添加系统定时任务
宝塔面板 → 计划任务，添加以下任务：

**1. 系统监控检查（每5分钟）**
```bash
任务名称：系统监控检查
执行周期：每5分钟
脚本内容：cd /www/wwwroot/your-domain.com && php think web3:monitor --send-alerts
```

**2. 清理过期数据（每天凌晨2点）**
```bash
任务名称：清理过期数据
执行周期：每天 02:00
脚本内容：cd /www/wwwroot/your-domain.com && php think clean:nonces && php think clean:logs
```

**3. 挖矿收益计算（每小时）**
```bash
任务名称：挖矿收益计算
执行周期：每小时
脚本内容：cd /www/wwwroot/your-domain.com && php think mining:calculate
```

**4. 数据库备份（每天凌晨3点）**
```bash
任务名称：数据库备份
执行周期：每天 03:00
脚本内容：mysqldump -u asset_user -p'your_password' digital_asset_db > /www/backup/asset_backup_$(date +%Y%m%d).sql
```

**5. 系统状态检查（每30分钟）**
```bash
任务名称：系统状态检查
执行周期：每30分钟
脚本内容：cd /www/wwwroot/your-domain.com && php think system:check
```

### 7.2 验证定时任务
```bash
# 手动测试监控任务
cd /www/wwwroot/your-domain.com
php think web3:monitor

# 查看定时任务日志
tail -f /www/wwwroot/your-domain.com/runtime/log/cron.log
```

## 🛡️ 第八步：安全配置

### 8.1 宝塔面板安全
```bash
# 修改面板端口（默认8888）
bt port 18888

# 设置面板SSL
bt ssl

# 绑定域名访问
bt domain panel.your-domain.com

# 设置IP白名单
bt whitelist add your-ip-address

# 开启BasicAuth
bt auth on
```

### 8.2 防火墙设置
宝塔面板 → 安全，配置防火墙规则：
- ✅ **开放端口**：80, 443, 22, 18888
- ❌ **关闭端口**：3306, 6379, 8080
- 🔒 **SSH端口**：建议修改默认22端口

### 8.3 网站安全加固
1. **开启防CC攻击**
2. **设置IP访问限制**
3. **配置防盗链保护**
4. **开启网站监控**

### 8.4 文件权限安全
```bash
# 设置敏感配置文件权限
chmod 600 /www/wwwroot/your-domain.com/application/database.php
chmod 600 /www/wwwroot/your-domain.com/application/extra/web3_monitor.php

# 禁止直接访问敏感目录
echo "deny all;" > /www/wwwroot/your-domain.com/application/.htaccess
echo "deny all;" > /www/wwwroot/your-domain.com/runtime/.htaccess
echo "deny all;" > /www/wwwroot/your-domain.com/vendor/.htaccess
```

## 📊 第九步：监控配置

### 9.1 系统监控设置
宝塔面板 → 监控，配置告警阈值：
```json
{
    "cpu_alert": 80,      // CPU使用率 > 80%
    "memory_alert": 85,   // 内存使用率 > 85%
    "disk_alert": 90,     // 磁盘使用率 > 90%
    "load_alert": 5,      // 系统负载 > 5
    "network_alert": 100  // 网络流量异常
}
```

### 9.2 业务系统监控
访问各个监控面板进行配置：

**Web3专项监控：**
- **监控仪表板**：`https://your-domain.com/admin/web3monitor/dashboard`
- **告警管理**：`https://your-domain.com/admin/web3monitor/alerts`
- **实时数据**：`https://your-domain.com/admin/web3monitor/realtime`

**业务系统监控：**
- **用户管理监控**：`https://your-domain.com/admin/user`
- **挖矿系统监控**：`https://your-domain.com/admin/mining`
- **资金流水监控**：`https://your-domain.com/admin/recharge`
- **钱包地址监控**：`https://your-domain.com/admin/casies`
- **系统日志监控**：`https://your-domain.com/admin/log`

### 9.3 邮件告警测试
```bash
# 测试监控告警功能
cd /www/wwwroot/your-domain.com
php think web3:monitor --test-alert

# 查看监控日志
tail -f /www/wwwroot/your-domain.com/runtime/log/web3_monitor.log
```

## 🧪 第十步：功能测试

### 10.1 基础功能测试
**网站访问测试：**
```bash
# 测试HTTP访问
curl -I http://your-domain.com

# 测试HTTPS访问
curl -I https://your-domain.com

# 测试管理后台
curl -I https://your-domain.com/admin
```

**数据库连接测试：**
```bash
# 测试数据库连接
mysql -u asset_user -p digital_asset_db -e "SELECT 1;"

# 检查数据库表
mysql -u asset_user -p digital_asset_db -e "SHOW TABLES;"

# 验证核心表数据
mysql -u asset_user -p digital_asset_db -e "SELECT COUNT(*) FROM tbl_users;"
```

### 10.2 核心功能测试

**用户管理功能测试：**
1. 访问管理后台：`https://your-domain.com/admin`
2. 测试用户登录功能
3. 验证用户权限控制
4. 测试用户增删改查功能

**钱包管理功能测试：**
1. 访问钱包管理：`https://your-domain.com/admin/casies`
2. 测试钱包地址添加
3. 验证助记词管理功能
4. 测试钱包地址查询和筛选

**挖矿系统功能测试：**
1. 访问挖矿管理：`https://your-domain.com/admin/mining`
2. 测试矿机配置管理
3. 验证挖矿记录功能
4. 测试收益计算和分配

**资金管理功能测试：**
1. 访问充值管理：`https://your-domain.com/admin/recharge`
2. 测试充值申请流程
3. 验证提现审核功能
4. 测试资金流水记录

### 10.3 Web3功能测试
**钱包连接测试步骤：**
1. 访问登录页面：`https://your-domain.com/admin`
2. 点击"连接钱包"按钮
3. 选择MetaMask钱包
4. 确认连接授权
5. 验证签名消息
6. 确认登录成功

**多链支持测试：**
1. 测试以太坊主网连接
2. 测试BSC网络切换
3. 测试Polygon网络切换
4. 验证网络自动检测功能

### 10.4 监控功能测试
```bash
# 手动触发系统监控检查
cd /www/wwwroot/your-domain.com
php think web3:monitor

# 查看监控数据
php think web3:monitor --show-stats

# 测试告警通知
php think web3:monitor --test-alert

# 测试业务系统监控
php think system:check

# 测试挖矿系统监控
php think mining:check
```

### 10.5 性能压力测试
```bash
# 使用ab工具进行压力测试
ab -n 1000 -c 10 https://your-domain.com/

# 测试管理后台性能
ab -n 500 -c 5 https://your-domain.com/admin/

# 测试API接口性能
ab -n 1000 -c 10 https://your-domain.com/admin/api/

# 监控系统资源
top
free -h
df -h
iostat 1 5
```

## 🔧 第十一步：性能优化

### 11.1 Nginx优化配置
编辑网站配置文件，添加优化配置：
```nginx
# 开启gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1k;
gzip_comp_level 6;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 设置缓存策略
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
}

# API接口优化
location /api/ {
    # 增加超时时间
    proxy_read_timeout 300;
    proxy_connect_timeout 300;
    proxy_send_timeout 300;

    # 允许大请求体
    client_max_body_size 10M;
}

# 限制请求频率
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
location /api/web3/ {
    limit_req zone=api burst=20 nodelay;
}

# 安全头配置
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

### 11.2 MySQL性能优化
宝塔面板 → MySQL → 配置修改：
```ini
[mysqld]
# 缓冲池配置
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 查询缓存
query_cache_size = 128M
query_cache_type = 1

# 连接配置
max_connections = 500
max_connect_errors = 10000

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

### 11.3 PHP性能优化
```ini
# OPcache配置
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=10000
opcache.revalidate_freq=2
opcache.fast_shutdown=1

# 实时编译优化
opcache.validate_timestamps=0  # 生产环境设为0
opcache.save_comments=0
opcache.enable_file_override=1
```

### 11.4 数据库索引优化
```sql
-- 创建必要的索引
-- 用户相关索引
CREATE INDEX idx_wallet_address ON tbl_users(wallet_address);
CREATE INDEX idx_user_email ON tbl_users(email);
CREATE INDEX idx_user_status ON tbl_users(is_delete);

-- Web3功能索引
CREATE INDEX idx_create_time ON web3_login_logs(create_time);
CREATE INDEX idx_login_status ON web3_login_logs(login_status);
CREATE INDEX idx_ip_address ON web3_login_logs(ip_address);
CREATE INDEX idx_expire_time ON wallet_nonces(expire_time);

-- 业务功能索引
CREATE INDEX idx_casies_address ON aw_casies(Address);
CREATE INDEX idx_casies_user ON aw_casies(user_id);
CREATE INDEX idx_mining_status ON aw_mining(status);
CREATE INDEX idx_mining_record_user ON aw_mining_record(fish_id);
CREATE INDEX idx_recharge_status ON recharge(status);
CREATE INDEX idx_withdraw_status ON aw_withdraw(status);

-- 分析表性能
ANALYZE TABLE tbl_users;
ANALYZE TABLE web3_login_logs;
ANALYZE TABLE wallet_nonces;
ANALYZE TABLE aw_casies;
ANALYZE TABLE aw_mining;
ANALYZE TABLE aw_mining_record;
ANALYZE TABLE recharge;
```

## 📋 第十二步：备份策略

### 12.1 自动备份配置
宝塔面板 → 计划任务，设置自动备份：

**网站文件备份（每周）：**
```bash
任务名称：网站文件备份
执行周期：每周日 01:00
备份类型：网站
保留份数：4份
备份到：本地磁盘 + 云存储
```

**数据库备份（每天）：**
```bash
任务名称：数据库备份
执行周期：每天 02:00
备份类型：数据库
保留份数：7份
压缩备份：是
```

### 12.2 手动备份脚本
创建完整备份脚本 `/root/web3_backup.sh`：
```bash
#!/bin/bash
BACKUP_DIR="/www/backup/digital_asset"
DATE=$(date +%Y%m%d_%H%M%S)
SITE_DIR="/www/wwwroot/your-domain.com"
DB_NAME="digital_asset_db"
DB_USER="asset_user"
DB_PASS="your_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份网站文件
tar -czf $BACKUP_DIR/asset_files_$DATE.tar.gz $SITE_DIR

# 备份数据库
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/asset_db_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete

echo "Backup completed: $DATE"
```

### 12.3 云存储备份
配置备份到云存储（推荐）：
- **阿里云OSS**
- **腾讯云COS**
- **七牛云存储**
- **百度云BOS**

## 🚨 故障排除指南

### 常见问题及解决方案

#### 1. 网站无法访问
**症状：** 浏览器显示"无法访问此网站"

**排查步骤：**
```bash
# 检查Nginx状态
systemctl status nginx
bt nginx status

# 检查PHP-FPM状态
systemctl status php-fpm
bt php status

# 检查端口占用
netstat -tulpn | grep :80
netstat -tulpn | grep :443

# 查看Nginx错误日志
tail -f /www/wwwroot/your-domain.com/log/error.log
tail -f /www/server/nginx/logs/error.log
```

**解决方案：**
```bash
# 重启服务
bt nginx restart
bt php restart

# 检查配置文件语法
nginx -t

# 重新加载配置
nginx -s reload
```

#### 2. 数据库连接失败
**症状：** 页面显示"数据库连接失败"

**排查步骤：**
```bash
# 检查MySQL状态
systemctl status mysql
bt mysql status

# 测试数据库连接
mysql -u asset_user -p digital_asset_db

# 查看MySQL错误日志
tail -f /var/log/mysql/error.log
```

**解决方案：**
```bash
# 重启MySQL
bt mysql restart

# 检查数据库配置
cat /www/wwwroot/your-domain.com/application/database.php

# 重置数据库密码
mysql -u root -e "ALTER USER 'asset_user'@'localhost' IDENTIFIED BY 'new_password';"
```

#### 3. Web3钱包连接失败
**症状：** 钱包连接按钮无响应或报错

**排查步骤：**
1. **检查HTTPS：** 确认网站使用HTTPS协议
2. **检查浏览器控制台：** 查看JavaScript错误
3. **验证MetaMask：** 确认钱包插件已安装
4. **检查网络配置：** 验证区块链网络设置

**解决方案：**
```bash
# 检查SSL证书
openssl s_client -servername your-domain.com -connect your-domain.com:443

# 查看前端错误日志
tail -f /www/wwwroot/your-domain.com/runtime/log/error.log

# 测试Web3服务
cd /www/wwwroot/your-domain.com
php think test:web3
```

#### 4. 监控告警不工作
**症状：** 监控系统无告警邮件发送

**排查步骤：**
```bash
# 检查定时任务
crontab -l

# 手动执行监控任务
cd /www/wwwroot/your-domain.com
php think web3:monitor

# 查看监控日志
tail -f /www/wwwroot/your-domain.com/runtime/log/web3_monitor.log

# 测试邮件发送
php think test:email
```

**解决方案：**
```bash
# 检查SMTP配置
cat /www/wwwroot/your-domain.com/application/extra/web3_monitor.php

# 测试SMTP连接
telnet smtp.exmail.qq.com 587

# 重新配置邮件服务
vi /www/wwwroot/your-domain.com/application/extra/web3_monitor.php
```

#### 5. 网站访问速度慢
**症状：** 页面加载时间过长

**排查步骤：**
```bash
# 检查系统负载
uptime
top
htop

# 检查磁盘IO
iostat 1 5

# 检查网络连接
netstat -an | grep :80 | wc -l

# 分析慢查询
mysql -u root -e "SHOW PROCESSLIST;"
```

**解决方案：**
```bash
# 开启OPcache
echo "opcache.enable=1" >> /www/server/php/80/etc/php.ini

# 优化MySQL
mysql -u root -e "SET GLOBAL query_cache_size = 134217728;"

# 清理日志文件
find /www/wwwroot/your-domain.com/runtime/log -name "*.log" -mtime +7 -delete
```

## 📞 技术支持

### 重要文件位置
```bash
# 配置文件
/www/wwwroot/your-domain.com/application/database.php          # 数据库配置
/www/wwwroot/your-domain.com/application/extra/web3_monitor.php # 监控配置
/www/server/panel/vhost/nginx/your-domain.com.conf             # Nginx配置

# 日志文件
/www/wwwroot/your-domain.com/log/access.log                     # 访问日志
/www/wwwroot/your-domain.com/log/error.log                      # 错误日志
/www/wwwroot/your-domain.com/runtime/log/                       # 应用日志
/www/wwwroot/your-domain.com/runtime/log/web3_monitor.log       # 监控日志

# 备份文件
/www/backup/                                                    # 备份目录
```

### 性能监控命令
```bash
# 系统监控
uptime                    # 系统负载
free -h                   # 内存使用
df -h                     # 磁盘使用
iostat 1 5               # IO统计
netstat -tulpn           # 网络连接

# 进程监控
ps aux | grep nginx      # Nginx进程
ps aux | grep php        # PHP进程
ps aux | grep mysql      # MySQL进程

# 日志监控
tail -f /var/log/messages                                      # 系统日志
tail -f /www/wwwroot/your-domain.com/runtime/log/error.log     # 应用错误日志
tail -f /www/server/nginx/logs/access.log                      # Nginx访问日志
```

### 应急处理流程
**1. 网站完全无法访问：**
```bash
# 快速重启所有服务
bt restart
bt nginx restart
bt php restart
bt mysql restart
```

**2. 数据库异常：**
```bash
# 备份当前数据
mysqldump -u web3_user -p web3_wallet_db > /tmp/emergency_backup.sql

# 修复数据库
mysql -u root -e "REPAIR TABLE web3_wallet_db.tbl_users;"
```

**3. 磁盘空间不足：**
```bash
# 清理日志文件
find /www/wwwroot/your-domain.com/runtime/log -name "*.log" -mtime +3 -delete

# 清理临时文件
rm -rf /tmp/*
rm -rf /www/wwwroot/your-domain.com/runtime/cache/*
```

## ✅ 部署检查清单

### 部署前检查
- [ ] 服务器配置满足最低要求（2核4GB）
- [ ] 域名已解析到服务器IP
- [ ] 宝塔面板安装完成
- [ ] 必要软件已安装（Nginx、PHP8.0+、MySQL8.0+）

### 环境配置检查
- [ ] PHP扩展安装完成（mysqli、pdo_mysql、curl、openssl、json、mbstring、gd）
- [ ] PHP配置优化完成（内存限制、执行时间、错误日志）
- [ ] MySQL配置优化完成（缓冲池、查询缓存）
- [ ] Nginx配置优化完成（gzip、缓存、安全头）

### 项目配置检查
- [ ] 项目文件上传完成
- [ ] 数据库创建并导入成功
- [ ] 数据库配置文件正确
- [ ] 监控配置文件正确
- [ ] 目录权限设置正确（runtime目录777权限）
- [ ] 敏感文件权限设置正确（600权限）

### 功能测试检查
- [ ] 网站首页正常访问
- [ ] 管理后台正常登录
- [ ] Web3钱包连接正常
- [ ] 多链切换功能正常
- [ ] 监控仪表板正常显示
- [ ] 邮件告警测试成功

### 安全配置检查
- [ ] SSL证书申请并配置成功
- [ ] HTTPS强制跳转开启
- [ ] 防火墙规则配置正确
- [ ] 宝塔面板安全加固完成
- [ ] 敏感目录访问限制设置
- [ ] 定时任务配置完成

### 监控告警检查
- [ ] 系统监控配置完成
- [ ] Web3专项监控正常运行
- [ ] 邮件告警配置正确
- [ ] 定时监控任务正常执行
- [ ] 监控日志记录正常

### 备份恢复检查
- [ ] 自动备份任务配置完成
- [ ] 手动备份脚本测试成功
- [ ] 云存储备份配置完成（可选）
- [ ] 恢复流程测试成功

## 🎉 部署完成

### 🎯 访问地址
恭喜！您已成功部署数字资产管理平台。以下是重要的访问地址：

**前台访问：**
- 🌐 **网站首页**：`https://your-domain.com`
- 🔐 **用户登录**：`https://your-domain.com/login`

**后台管理：**
- 🛠️ **管理后台**：`https://your-domain.com/admin`
- � **用户管理**：`https://your-domain.com/admin/user`
- 💰 **钱包管理**：`https://your-domain.com/admin/casies`
- ⛏️ **挖矿管理**：`https://your-domain.com/admin/mining`
- 💳 **充值管理**：`https://your-domain.com/admin/recharge`
- 💸 **提现管理**：`https://your-domain.com/admin/withdraw`
- �📊 **Web3监控**：`https://your-domain.com/admin/web3monitor/dashboard`
- 🚨 **告警管理**：`https://your-domain.com/admin/web3monitor/alerts`
- � **系统日志**：`https://your-domain.com/admin/log`

### 🔑 重要信息记录
请妥善保存以下信息：

**数据库信息：**
```
数据库名：web3_wallet_db
用户名：web3_user
密码：[您设置的密码]
主机：127.0.0.1
端口：3306
```

**配置文件位置：**
```
数据库配置：/www/wwwroot/your-domain.com/application/database.php
监控配置：/www/wwwroot/your-domain.com/application/extra/web3_monitor.php
Nginx配置：/www/server/panel/vhost/nginx/your-domain.com.conf
```

### 📋 下一步建议

**1. 安全加固（必做）：**
- 修改宝塔面板默认端口和密码
- 设置SSH密钥登录，禁用密码登录
- 配置防火墙白名单
- 定期更新系统和软件

**2. 性能优化（推荐）：**
- 配置CDN加速
- 启用Redis缓存
- 优化数据库查询
- 监控系统性能指标

**3. 运维管理（重要）：**
- 定期检查系统日志
- 监控磁盘空间使用
- 备份数据库和文件
- 更新安全补丁

**4. 功能扩展（可选）：**
- 集成更多钱包类型
- 添加更多区块链网络
- 扩展监控告警功能
- 开发移动端应用

### 📚 相关文档

**项目文档：**
- `BAOTA_DEPLOYMENT_GUIDE.md` - 详细部署指南
- `MULTICHAIN_MONITORING_GUIDE.md` - 多链监控指南
- `WEB3_DEPLOYMENT_GUIDE.md` - Web3部署指南
- `README_DEPLOYMENT.md` - 部署文档总览

**脚本工具：**
- `deploy_baota.sh` - 自动部署脚本
- `check_baota_status.sh` - 系统状态检查脚本

### 🆘 获取帮助

如果在部署或使用过程中遇到问题：

1. **查看日志文件**：详细的错误信息通常在相应的日志文件中
2. **使用检查脚本**：运行 `./check_baota_status.sh` 进行系统诊断
3. **参考故障排除**：查看本文档的"故障排除指南"章节
4. **社区支持**：
   - 宝塔论坛：https://www.bt.cn/bbs/
   - ThinkPHP文档：https://www.thinkphp.cn/
   - Web3开发文档：https://docs.ethers.io/

### 🔄 定期维护

**每日检查：**
- 查看系统监控数据
- 检查错误日志
- 验证备份任务执行

**每周检查：**
- 更新系统补丁
- 清理过期日志
- 检查磁盘空间

**每月检查：**
- 更新软件版本
- 优化数据库性能
- 审查安全配置

---

## 📝 附录

### A. 常用命令速查

**宝塔面板管理：**
```bash
bt restart          # 重启面板
bt stop             # 停止面板
bt start            # 启动面板
bt status           # 查看状态
bt info             # 查看面板信息
bt password         # 修改密码
bt port 8888        # 修改端口
```

**服务管理：**
```bash
bt nginx restart    # 重启Nginx
bt php restart      # 重启PHP
bt mysql restart    # 重启MySQL
bt redis restart    # 重启Redis
```

**项目管理：**
```bash
cd /www/wwwroot/your-domain.com
php think web3:monitor              # 执行监控检查
php think clean:nonces              # 清理过期nonce
php think test:web3                 # 测试Web3功能
php think test:email                # 测试邮件发送
```

### B. 性能基准参考

**系统性能指标：**
- CPU使用率：< 70%
- 内存使用率：< 80%
- 磁盘使用率：< 85%
- 系统负载：< 核心数

**Web性能指标：**
- 页面加载时间：< 3秒
- API响应时间：< 500ms
- 并发用户数：> 100
- 可用性：> 99.9%

**数据库性能指标：**
- 查询响应时间：< 100ms
- 慢查询比例：< 1%
- 连接数使用率：< 80%
- 缓存命中率：> 90%

---

**🎊 部署完成！祝您使用愉快！**

如有任何问题，请参考相关文档或寻求技术支持。
