#!/bin/bash

# Web3钱包登录系统 - 宝塔面板自动部署脚本
# 使用方法: chmod +x deploy_baota.sh && ./deploy_baota.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查宝塔面板是否安装
check_baota() {
    if ! command -v bt &> /dev/null; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        log_info "安装命令: wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh"
        exit 1
    fi
    log_success "宝塔面板已安装"
}

# 获取用户输入
get_user_input() {
    echo "==================================="
    echo "Web3钱包登录系统部署配置"
    echo "==================================="
    
    read -p "请输入网站域名 (例: web3.example.com): " DOMAIN
    read -p "请输入数据库名称 [web3_wallet_db]: " DB_NAME
    DB_NAME=${DB_NAME:-web3_wallet_db}
    
    read -p "请输入数据库用户名 [web3_user]: " DB_USER
    DB_USER=${DB_USER:-web3_user}
    
    read -s -p "请输入数据库密码: " DB_PASS
    echo
    
    read -p "请输入管理员邮箱 (用于接收告警): " ADMIN_EMAIL
    
    read -p "是否启用SSL证书? (y/n) [y]: " ENABLE_SSL
    ENABLE_SSL=${ENABLE_SSL:-y}
    
    echo
    log_info "配置信息确认:"
    echo "域名: $DOMAIN"
    echo "数据库名: $DB_NAME"
    echo "数据库用户: $DB_USER"
    echo "管理员邮箱: $ADMIN_EMAIL"
    echo "启用SSL: $ENABLE_SSL"
    echo
    
    read -p "确认以上配置? (y/n): " CONFIRM
    if [[ $CONFIRM != "y" && $CONFIRM != "Y" ]]; then
        log_error "部署已取消"
        exit 1
    fi
}

# 检查必要软件
check_software() {
    log_info "检查必要软件..."
    
    # 检查PHP
    if ! bt php status | grep -q "running"; then
        log_error "PHP未运行，请在宝塔面板中安装并启动PHP 8.0+"
        exit 1
    fi
    
    # 检查MySQL
    if ! bt mysql status | grep -q "running"; then
        log_error "MySQL未运行，请在宝塔面板中安装并启动MySQL 8.0+"
        exit 1
    fi
    
    # 检查Nginx
    if ! bt nginx status | grep -q "running"; then
        log_error "Nginx未运行，请在宝塔面板中安装并启动Nginx"
        exit 1
    fi
    
    log_success "必要软件检查通过"
}

# 创建网站目录
create_site_directory() {
    log_info "创建网站目录..."
    
    SITE_DIR="/www/wwwroot/$DOMAIN"
    
    if [ -d "$SITE_DIR" ]; then
        log_warning "网站目录已存在: $SITE_DIR"
        read -p "是否覆盖现有目录? (y/n): " OVERWRITE
        if [[ $OVERWRITE == "y" || $OVERWRITE == "Y" ]]; then
            rm -rf "$SITE_DIR"
        else
            log_error "部署已取消"
            exit 1
        fi
    fi
    
    mkdir -p "$SITE_DIR"
    log_success "网站目录创建完成: $SITE_DIR"
}

# 复制项目文件
copy_project_files() {
    log_info "复制项目文件..."
    
    CURRENT_DIR=$(pwd)
    
    # 复制所有文件到网站目录
    cp -r "$CURRENT_DIR"/* "$SITE_DIR/"
    
    # 设置权限
    chown -R www:www "$SITE_DIR"
    chmod -R 755 "$SITE_DIR"
    chmod -R 777 "$SITE_DIR/runtime"
    
    log_success "项目文件复制完成"
}

# 创建数据库
create_database() {
    log_info "创建数据库..."
    
    # 生成随机密码（如果用户没有提供）
    if [ -z "$DB_PASS" ]; then
        DB_PASS=$(openssl rand -base64 12)
        log_info "生成的数据库密码: $DB_PASS"
    fi
    
    # 创建数据库和用户
    mysql -u root -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    mysql -u root -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';"
    mysql -u root -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';"
    mysql -u root -e "FLUSH PRIVILEGES;"
    
    # 导入数据库结构
    if [ -f "$SITE_DIR/web3_migration.sql" ]; then
        mysql -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$SITE_DIR/web3_migration.sql"
        log_success "数据库结构导入完成"
    else
        log_warning "未找到数据库迁移文件"
    fi
    
    log_success "数据库创建完成"
}

# 配置项目
configure_project() {
    log_info "配置项目..."
    
    # 配置数据库连接
    cat > "$SITE_DIR/application/database.php" << EOF
<?php
return [
    'type'            => 'mysql',
    'hostname'        => '127.0.0.1',
    'database'        => '$DB_NAME',
    'username'        => '$DB_USER',
    'password'        => '$DB_PASS',
    'hostport'        => '3306',
    'charset'         => 'utf8mb4',
    'prefix'          => '',
    'debug'           => false,
    'deploy'          => 0,
    'rw_separate'     => false,
    'master_num'      => 1,
    'slave_no'        => '',
    'fields_strict'   => true,
    'resultset_type'  => 'array',
    'auto_timestamp'  => false,
    'datetime_format' => 'Y-m-d H:i:s',
    'sql_explain'     => false,
];
EOF

    # 配置监控告警
    cat > "$SITE_DIR/application/extra/web3_monitor.php" << EOF
<?php
return [
    'enabled' => true,
    'check_interval' => 300,
    'notifications' => [
        'email' => [
            'enabled' => true,
            'smtp_host' => 'smtp.exmail.qq.com',
            'smtp_port' => 587,
            'smtp_username' => '$ADMIN_EMAIL',
            'smtp_password' => 'your_email_password',
            'from_email' => '$ADMIN_EMAIL',
            'from_name' => 'Web3监控系统',
            'to_emails' => ['$ADMIN_EMAIL'],
            'alert_levels' => ['critical', 'error']
        ]
    ],
    'thresholds' => [
        'failed_login_rate' => 0.3,
        'rapid_login_count' => 20,
        'suspicious_ip_count' => 10,
        'invalid_signature_rate' => 0.2,
        'expired_nonce_count' => 1000,
        'log_table_size' => 100000
    ]
];
EOF

    # 设置敏感文件权限
    chmod 600 "$SITE_DIR/application/database.php"
    chmod 600 "$SITE_DIR/application/extra/web3_monitor.php"
    
    log_success "项目配置完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    # 创建Nginx配置文件
    cat > "/www/server/panel/vhost/nginx/$DOMAIN.conf" << EOF
server {
    listen 80;
    server_name $DOMAIN;
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/$DOMAIN/public;
    
    # SSL配置将在后续添加
    
    # 防止访问隐藏文件
    location ~ /\. {
        deny all;
    }
    
    # ThinkPHP URL重写
    location / {
        if (!-e \$request_filename) {
            rewrite ^(.*)\$ /index.php?s=/\$1 last;
            break;
        }
    }
    
    # PHP处理
    location ~ \.php\$ {
        fastcgi_pass unix:/tmp/php-cgi-80.sock;
        fastcgi_index index.php;
        include fastcgi.conf;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 访问日志
    access_log /www/wwwroot/$DOMAIN/log/access.log;
    error_log /www/wwwroot/$DOMAIN/log/error.log;
}
EOF

    # 创建日志目录
    mkdir -p "/www/wwwroot/$DOMAIN/log"
    chown www:www "/www/wwwroot/$DOMAIN/log"
    
    # 重启Nginx
    bt nginx restart
    
    log_success "Nginx配置完成"
}

# 配置SSL证书
configure_ssl() {
    if [[ $ENABLE_SSL == "y" || $ENABLE_SSL == "Y" ]]; then
        log_info "配置SSL证书..."
        log_warning "请在宝塔面板中手动申请SSL证书"
        log_info "路径: 网站 -> $DOMAIN -> SSL -> Let's Encrypt"
    fi
}

# 添加定时任务
add_cron_jobs() {
    log_info "添加定时任务..."
    
    # Web3监控任务
    (crontab -l 2>/dev/null; echo "*/5 * * * * cd /www/wwwroot/$DOMAIN && php think web3:monitor --send-alerts") | crontab -
    
    # 清理过期nonce任务
    (crontab -l 2>/dev/null; echo "0 2 * * * cd /www/wwwroot/$DOMAIN && php think clean:nonces") | crontab -
    
    # 数据库备份任务
    (crontab -l 2>/dev/null; echo "0 3 * * * mysqldump -u $DB_USER -p'$DB_PASS' $DB_NAME > /www/backup/web3_backup_\$(date +\\%Y\\%m\\%d).sql") | crontab -
    
    log_success "定时任务添加完成"
}

# 创建备份目录
create_backup_directory() {
    log_info "创建备份目录..."
    
    mkdir -p /www/backup
    chown www:www /www/backup
    
    log_success "备份目录创建完成"
}

# 测试部署
test_deployment() {
    log_info "测试部署..."
    
    # 测试网站访问
    if curl -s -o /dev/null -w "%{http_code}" "http://$DOMAIN" | grep -q "200\|301\|302"; then
        log_success "网站访问正常"
    else
        log_warning "网站访问可能有问题，请检查配置"
    fi
    
    # 测试数据库连接
    if mysql -u "$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME; SELECT 1;" &>/dev/null; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
    fi
}

# 显示部署结果
show_deployment_result() {
    echo
    echo "==================================="
    echo "部署完成！"
    echo "==================================="
    echo
    log_success "网站地址: http://$DOMAIN"
    log_success "管理后台: http://$DOMAIN/admin"
    log_success "监控面板: http://$DOMAIN/admin/web3monitor/dashboard"
    echo
    echo "数据库信息:"
    echo "  数据库名: $DB_NAME"
    echo "  用户名: $DB_USER"
    echo "  密码: $DB_PASS"
    echo
    echo "重要提醒:"
    echo "1. 请在宝塔面板中为域名申请SSL证书"
    echo "2. 请修改监控配置中的邮箱密码"
    echo "3. 请定期检查系统日志和监控数据"
    echo "4. 建议修改默认的管理员账户密码"
    echo
    echo "配置文件位置:"
    echo "  数据库配置: $SITE_DIR/application/database.php"
    echo "  监控配置: $SITE_DIR/application/extra/web3_monitor.php"
    echo "  Nginx配置: /www/server/panel/vhost/nginx/$DOMAIN.conf"
    echo
    echo "日志文件位置:"
    echo "  访问日志: /www/wwwroot/$DOMAIN/log/access.log"
    echo "  错误日志: /www/wwwroot/$DOMAIN/log/error.log"
    echo "  应用日志: /www/wwwroot/$DOMAIN/runtime/log/"
    echo
    echo "如有问题，请查看部署文档: BAOTA_DEPLOYMENT_GUIDE.md"
    echo "==================================="
}

# 主函数
main() {
    echo "Web3钱包登录系统 - 宝塔面板自动部署脚本"
    echo "================================================"
    
    check_root
    check_baota
    get_user_input
    check_software
    create_site_directory
    copy_project_files
    create_database
    configure_project
    configure_nginx
    configure_ssl
    add_cron_jobs
    create_backup_directory
    test_deployment
    show_deployment_result
    
    log_success "部署脚本执行完成！"
}

# 执行主函数
main "$@"
