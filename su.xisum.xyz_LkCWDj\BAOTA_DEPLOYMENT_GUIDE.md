# Web3钱包登录系统 - 宝塔面板部署教程

## 📋 部署概述

本教程将指导您在宝塔面板环境下完整部署Web3钱包登录系统，包括：
- 基础Web3钱包登录功能
- 多链支持（Ethereum、BSC、Polygon、Arbitrum、Optimism）
- 监控告警系统
- 自动化运维工具

## 🛠️ 环境要求

### 服务器配置
- **操作系统**：CentOS 7.0+ / Ubuntu 18.04+ / Debian 9.0+
- **内存**：最低2GB，推荐4GB+
- **硬盘**：最低20GB，推荐50GB+
- **CPU**：最低2核，推荐4核+

### 软件环境
- **宝塔面板**：7.7.0+
- **PHP**：7.4+ 或 8.0+
- **MySQL**：5.7+ 或 8.0+
- **Nginx**：1.18+
- **Redis**：6.0+（可选，用于缓存）

## 📦 第一步：安装宝塔面板

### 1.1 安装宝塔面板

**CentOS系统：**
```bash
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh ed8484bec
```

**Ubuntu/Debian系统：**
```bash
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh ed8484bec
```

### 1.2 配置宝塔面板
1. 安装完成后，记录面板地址、用户名和密码
2. 登录宝塔面板：`http://your-server-ip:8888`
3. 绑定宝塔账号（推荐）

### 1.3 安装软件环境
在宝塔面板 → 软件商店中安装：

**必装软件：**
- **Nginx** 1.20+
- **MySQL** 8.0+
- **PHP** 8.0+
- **phpMyAdmin**（数据库管理）

**可选软件：**
- **Redis** 6.0+（缓存）
- **Supervisor**（进程管理）
- **PM2管理器**（Node.js进程管理）

## 🗄️ 第二步：数据库配置

### 2.1 创建数据库
1. 进入宝塔面板 → 数据库
2. 点击"添加数据库"
3. 填写信息：
   - **数据库名**：`web3_wallet_db`
   - **用户名**：`web3_user`
   - **密码**：生成强密码
   - **访问权限**：本地服务器

### 2.2 导入数据库结构
1. 点击数据库名称进入phpMyAdmin
2. 选择"导入"选项卡
3. 上传并执行 `web3_migration.sql` 文件

**或者使用命令行：**
```bash
# 进入网站根目录
cd /www/wwwroot/your-domain.com

# 导入数据库
mysql -u web3_user -p web3_wallet_db < web3_migration.sql
```

### 2.3 验证数据库
确认以下表已创建：
- `tbl_users`（用户表，已扩展Web3字段）
- `web3_login_logs`（Web3登录日志表）
- `wallet_nonces`（钱包nonce缓存表）

## 🌐 第三步：网站配置

### 3.1 创建网站
1. 进入宝塔面板 → 网站
2. 点击"添加站点"
3. 填写信息：
   - **域名**：`your-domain.com`
   - **根目录**：`/www/wwwroot/your-domain.com`
   - **PHP版本**：8.0
   - **数据库**：选择已创建的数据库

### 3.2 上传项目文件
1. 使用宝塔文件管理器或FTP上传项目文件
2. 确保项目结构如下：
```
/www/wwwroot/your-domain.com/
├── application/
├── public/
├── runtime/
├── vendor/
├── web3_migration.sql
├── BAOTA_DEPLOYMENT_GUIDE.md
└── 其他项目文件
```

### 3.3 设置网站运行目录
1. 点击网站名称 → 设置
2. 网站目录 → 运行目录：设置为 `/public`
3. 防跨站攻击：关闭
4. 保存设置

### 3.4 配置伪静态
在网站设置 → 伪静态中添加ThinkPHP规则：
```nginx
location / {
    if (!-e $request_filename) {
        rewrite ^(.*)$ /index.php?s=/$1 last;
        break;
    }
}
```

## 🔧 第四步：PHP配置

### 4.1 安装PHP扩展
在宝塔面板 → PHP设置 → 安装扩展中安装：
- **mysqli**（数据库连接）
- **pdo_mysql**（数据库PDO）
- **curl**（HTTP请求）
- **openssl**（加密支持）
- **json**（JSON处理）
- **mbstring**（多字节字符串）
- **gd**（图像处理）
- **redis**（Redis缓存，可选）

### 4.2 修改PHP配置
在PHP设置 → 配置修改中调整：
```ini
# 基础配置
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
post_max_size = 50M
upload_max_filesize = 50M

# 错误报告
display_errors = Off
log_errors = On
error_log = /www/wwwroot/your-domain.com/runtime/log/php_error.log

# 时区设置
date.timezone = Asia/Shanghai
```

### 4.3 设置目录权限
```bash
# 设置项目目录权限
chown -R www:www /www/wwwroot/your-domain.com
chmod -R 755 /www/wwwroot/your-domain.com

# 设置运行时目录权限
chmod -R 777 /www/wwwroot/your-domain.com/runtime
```

## ⚙️ 第五步：项目配置

### 5.1 数据库配置
编辑 `application/database.php`：
```php
return [
    'type'            => 'mysql',
    'hostname'        => '127.0.0.1',
    'database'        => 'web3_wallet_db',
    'username'        => 'web3_user',
    'password'        => 'your_database_password',
    'hostport'        => '3306',
    'charset'         => 'utf8mb4',
    'prefix'          => '',
    // 其他配置...
];
```

### 5.2 应用配置
编辑 `application/config.php`：
```php
return [
    'app_debug'               => false,  // 生产环境关闭调试
    'app_trace'               => false,  // 关闭页面Trace
    'url_route_on'            => true,   // 开启路由
    'url_route_must'          => false,  // 路由必须
    'url_html_suffix'         => '',     // URL后缀
    'default_timezone'        => 'Asia/Shanghai',
    // 其他配置...
];
```

### 5.3 Web3监控配置
编辑 `application/extra/web3_monitor.php`：
```php
return [
    'enabled' => true,
    'notifications' => [
        'email' => [
            'enabled' => true,
            'smtp_host' => 'smtp.your-domain.com',
            'smtp_port' => 587,
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'your_email_password',
            'from_email' => '<EMAIL>',
            'to_emails' => ['<EMAIL>'],
        ]
    ],
    // 其他配置...
];
```

## 🔒 第六步：SSL证书配置

### 6.1 申请SSL证书
1. 进入网站设置 → SSL
2. 选择"Let's Encrypt"免费证书
3. 填写邮箱地址
4. 点击申请证书

### 6.2 强制HTTPS
1. 开启"强制HTTPS"
2. 设置HSTS
3. 验证HTTPS访问正常

**注意：Web3钱包连接必须在HTTPS环境下工作！**

## ⏰ 第七步：定时任务配置

### 7.1 添加定时任务
在宝塔面板 → 计划任务中添加：

**1. Web3监控检查（每5分钟）**
```bash
# 任务名称：Web3监控检查
# 执行周期：每5分钟
# 脚本内容：
cd /www/wwwroot/your-domain.com && php think web3:monitor --send-alerts
```

**2. 清理过期nonce（每天凌晨2点）**
```bash
# 任务名称：清理过期nonce
# 执行周期：每天 02:00
# 脚本内容：
cd /www/wwwroot/your-domain.com && php think clean:nonces
```

**3. 数据库备份（每天凌晨3点）**
```bash
# 任务名称：数据库备份
# 执行周期：每天 03:00
# 脚本内容：
mysqldump -u web3_user -p'your_password' web3_wallet_db > /www/backup/web3_backup_$(date +%Y%m%d).sql
```

### 7.2 验证定时任务
```bash
# 手动执行监控任务测试
cd /www/wwwroot/your-domain.com
php think web3:monitor

# 查看任务执行日志
tail -f /www/wwwroot/your-domain.com/runtime/log/cron.log
```

## 🛡️ 第八步：安全配置

### 8.1 防火墙设置
在宝塔面板 → 安全中配置：
- 开放端口：80, 443, 22, 8888
- 关闭不必要的端口
- 设置SSH端口（建议修改默认22端口）

### 8.2 网站安全
1. **开启防CC攻击**
2. **设置IP白名单**（管理后台）
3. **开启防盗链**
4. **配置访问限制**

### 8.3 文件权限安全
```bash
# 设置敏感文件权限
chmod 600 /www/wwwroot/your-domain.com/application/database.php
chmod 600 /www/wwwroot/your-domain.com/application/extra/web3_monitor.php

# 禁止直接访问敏感目录
echo "deny all;" > /www/wwwroot/your-domain.com/application/.htaccess
echo "deny all;" > /www/wwwroot/your-domain.com/runtime/.htaccess
```

## 📊 第九步：监控配置

### 9.1 系统监控
在宝塔面板 → 监控中设置：
- CPU使用率告警：>80%
- 内存使用率告警：>85%
- 磁盘使用率告警：>90%
- 负载告警：>5

### 9.2 网站监控
- 开启网站监控
- 设置监控频率：5分钟
- 配置告警邮箱

### 9.3 Web3专项监控
访问监控面板：
- 监控仪表板：`https://your-domain.com/admin/web3monitor/dashboard`
- 告警管理：`https://your-domain.com/admin/web3monitor/alerts`

## 🧪 第十步：功能测试

### 10.1 基础功能测试
1. **访问网站首页**：确认正常加载
2. **数据库连接**：检查数据库连接正常
3. **PHP功能**：确认PHP扩展正常

### 10.2 Web3功能测试
1. **钱包连接测试**：
   - 访问登录页面
   - 测试MetaMask连接
   - 验证网络切换功能

2. **多链支持测试**：
   - 测试以太坊网络登录
   - 测试BSC网络登录
   - 测试Polygon网络登录

3. **监控功能测试**：
   - 访问监控仪表板
   - 触发监控检查
   - 验证告警通知

### 10.3 性能测试
```bash
# 使用ab工具进行压力测试
ab -n 1000 -c 10 https://your-domain.com/

# 监控系统资源使用
top
free -h
df -h
```

## 🔧 第十一步：优化配置

### 11.1 Nginx优化
编辑网站配置文件：
```nginx
# 开启gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1k;
gzip_comp_level 6;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 设置缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 30d;
    add_header Cache-Control "public, immutable";
}

# 限制请求频率
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
location /api/ {
    limit_req zone=api burst=20 nodelay;
}
```

### 11.2 MySQL优化
在宝塔面板 → MySQL → 配置修改：
```ini
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_size = 128M
query_cache_type = 1
```

### 11.3 PHP优化
```ini
# OPcache配置
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=10000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

## 📋 第十二步：备份策略

### 12.1 自动备份配置
在宝塔面板 → 计划任务中设置：

**1. 网站文件备份（每周）**
```bash
# 任务名称：网站文件备份
# 执行周期：每周日 01:00
# 备份类型：网站
# 保留份数：4份
```

**2. 数据库备份（每天）**
```bash
# 任务名称：数据库备份
# 执行周期：每天 02:00
# 备份类型：数据库
# 保留份数：7份
```

### 12.2 远程备份
配置备份到云存储：
- 阿里云OSS
- 腾讯云COS
- 七牛云存储

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 网站无法访问
```bash
# 检查Nginx状态
systemctl status nginx

# 检查PHP-FPM状态
systemctl status php-fpm

# 查看错误日志
tail -f /www/wwwroot/your-domain.com/runtime/log/error.log
```

#### 2. 数据库连接失败
```bash
# 检查MySQL状态
systemctl status mysql

# 测试数据库连接
mysql -u web3_user -p web3_wallet_db

# 查看MySQL错误日志
tail -f /var/log/mysql/error.log
```

#### 3. Web3钱包连接失败
- 确认网站使用HTTPS
- 检查浏览器控制台错误
- 验证MetaMask是否安装
- 确认网络配置正确

#### 4. 监控告警不工作
```bash
# 检查定时任务执行
crontab -l

# 手动执行监控任务
cd /www/wwwroot/your-domain.com
php think web3:monitor

# 查看监控日志
tail -f /www/wwwroot/your-domain.com/runtime/log/web3_monitor.log
```

## 📞 技术支持

### 日志文件位置
- **Nginx访问日志**：`/www/wwwroot/your-domain.com/log/access.log`
- **Nginx错误日志**：`/www/wwwroot/your-domain.com/log/error.log`
- **PHP错误日志**：`/www/wwwroot/your-domain.com/runtime/log/php_error.log`
- **应用日志**：`/www/wwwroot/your-domain.com/runtime/log/`
- **Web3监控日志**：`/www/wwwroot/your-domain.com/runtime/log/web3_monitor.log`

### 性能监控命令
```bash
# 查看系统负载
uptime

# 查看内存使用
free -h

# 查看磁盘使用
df -h

# 查看进程状态
ps aux | grep php
ps aux | grep nginx
ps aux | grep mysql

# 查看网络连接
netstat -tulpn
```

## ✅ 部署检查清单

### 部署前检查
- [ ] 服务器配置满足要求
- [ ] 宝塔面板安装完成
- [ ] 域名解析配置正确
- [ ] SSL证书申请成功

### 环境配置检查
- [ ] PHP 8.0+ 安装完成
- [ ] MySQL 8.0+ 安装完成
- [ ] Nginx 1.20+ 安装完成
- [ ] 必要的PHP扩展已安装

### 项目配置检查
- [ ] 项目文件上传完成
- [ ] 数据库创建并导入成功
- [ ] 配置文件修改正确
- [ ] 目录权限设置正确

### 功能测试检查
- [ ] 网站首页正常访问
- [ ] 管理后台正常登录
- [ ] Web3钱包连接正常
- [ ] 多链切换功能正常
- [ ] 监控仪表板正常显示

### 安全配置检查
- [ ] HTTPS强制跳转开启
- [ ] 防火墙规则配置正确
- [ ] 敏感文件权限设置正确
- [ ] 定时任务配置完成

### 监控告警检查
- [ ] 监控任务正常执行
- [ ] 告警通知配置正确
- [ ] 邮件通知测试成功
- [ ] 系统监控正常运行

## 🎉 部署完成

恭喜！您已成功在宝塔面板环境下部署了完整的Web3钱包登录系统。

### 访问地址
- **网站首页**：`https://your-domain.com`
- **管理后台**：`https://your-domain.com/admin`
- **监控仪表板**：`https://your-domain.com/admin/web3monitor/dashboard`

### 下一步建议
1. 定期检查系统日志和监控数据
2. 根据实际使用情况调整监控阈值
3. 定期更新系统和应用程序
4. 制定应急响应预案
5. 培训相关运维人员

如有任何问题，请参考故障排除章节或联系技术支持。

---

## 📚 附录

### A. 宝塔面板常用命令

#### 面板管理命令
```bash
# 重启宝塔面板
bt restart

# 停止宝塔面板
bt stop

# 启动宝塔面板
bt start

# 查看面板状态
bt status

# 修改面板端口
bt port 8888

# 修改面板密码
bt password

# 查看面板信息
bt info
```

#### 服务管理命令
```bash
# 重启Nginx
bt nginx restart

# 重启PHP
bt php restart

# 重启MySQL
bt mysql restart

# 查看服务状态
bt nginx status
bt php status
bt mysql status
```

### B. Web3项目特定配置

#### B.1 Nginx配置优化
在网站设置 → 配置文件中添加：
```nginx
# Web3钱包专用配置
location /api/web3 {
    # 增加超时时间（钱包签名可能较慢）
    proxy_read_timeout 300;
    proxy_connect_timeout 300;
    proxy_send_timeout 300;

    # 允许大的请求体（签名数据）
    client_max_body_size 10M;

    # CORS配置（如果需要跨域）
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
}

# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
}

# 安全头配置
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

#### B.2 PHP配置调优
在PHP设置 → 配置修改中优化：
```ini
# Web3相关配置
max_execution_time = 300
max_input_time = 300
memory_limit = 512M
post_max_size = 50M
upload_max_filesize = 50M

# 会话配置
session.gc_maxlifetime = 7200
session.cookie_lifetime = 7200
session.cookie_secure = 1
session.cookie_httponly = 1

# 错误处理
display_errors = Off
log_errors = On
error_reporting = E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED

# 时区和字符集
date.timezone = Asia/Shanghai
default_charset = UTF-8
```

### C. 监控告警配置模板

#### C.1 邮件告警模板
```php
// application/extra/web3_monitor.php
return [
    'enabled' => true,
    'check_interval' => 300,

    'notifications' => [
        'email' => [
            'enabled' => true,
            'smtp_host' => 'smtp.exmail.qq.com',  // 腾讯企业邮箱
            'smtp_port' => 587,
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'your_email_password',
            'from_email' => '<EMAIL>',
            'from_name' => 'Web3监控系统',
            'to_emails' => [
                '<EMAIL>',
                '<EMAIL>'
            ],
            'alert_levels' => ['critical', 'error']
        ],

        'dingtalk' => [
            'enabled' => true,
            'webhook_url' => 'https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN',
            'secret' => 'YOUR_SECRET',
            'alert_levels' => ['critical', 'error', 'warning']
        ]
    ],

    'thresholds' => [
        'failed_login_rate' => 0.3,
        'rapid_login_count' => 20,
        'suspicious_ip_count' => 10,
        'invalid_signature_rate' => 0.2,
        'expired_nonce_count' => 1000,
        'log_table_size' => 100000
    ]
];
```

#### C.2 宝塔监控配置
在宝塔面板 → 监控中设置：
```json
{
    "cpu_alert": 80,
    "memory_alert": 85,
    "disk_alert": 90,
    "load_alert": 5,
    "network_alert": 100,
    "process_alert": [
        "nginx",
        "php-fpm",
        "mysql"
    ]
}
```

### D. 性能优化建议

#### D.1 数据库优化
```sql
-- 创建必要的索引
CREATE INDEX idx_wallet_address ON tbl_users(wallet_address);
CREATE INDEX idx_create_time ON web3_login_logs(create_time);
CREATE INDEX idx_login_status ON web3_login_logs(login_status);
CREATE INDEX idx_ip_address ON web3_login_logs(ip_address);
CREATE INDEX idx_expire_time ON wallet_nonces(expire_time);

-- 定期清理数据
DELETE FROM web3_login_logs WHERE create_time < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 90 DAY));
DELETE FROM wallet_nonces WHERE expire_time < UNIX_TIMESTAMP();
```

#### D.2 缓存配置
如果安装了Redis，在应用配置中启用：
```php
// application/config.php
'cache' => [
    'type'   => 'redis',
    'host'   => '127.0.0.1',
    'port'   => 6379,
    'password' => '',
    'select' => 0,
    'timeout' => 0,
    'expire' => 0,
    'persistent' => false,
    'prefix' => 'web3_',
];
```

### E. 安全加固指南

#### E.1 宝塔面板安全
```bash
# 修改默认端口
bt port 18888

# 设置面板SSL
bt ssl

# 绑定域名访问
bt domain your-panel-domain.com

# 设置IP白名单
bt whitelist add your-ip

# 开启BasicAuth
bt auth on
```

#### E.2 系统安全加固
```bash
# 禁用root SSH登录
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config

# 修改SSH端口
sed -i 's/#Port 22/Port 2222/' /etc/ssh/sshd_config

# 重启SSH服务
systemctl restart sshd

# 设置防火墙
ufw enable
ufw allow 2222/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 18888/tcp
```

### F. 备份恢复流程

#### F.1 完整备份脚本
```bash
#!/bin/bash
# 创建备份脚本 /root/web3_backup.sh

BACKUP_DIR="/www/backup/web3"
DATE=$(date +%Y%m%d_%H%M%S)
SITE_DIR="/www/wwwroot/your-domain.com"
DB_NAME="web3_wallet_db"
DB_USER="web3_user"
DB_PASS="your_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份网站文件
tar -czf $BACKUP_DIR/web3_files_$DATE.tar.gz $SITE_DIR

# 备份数据库
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/web3_db_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete

echo "Backup completed: $DATE"
```

#### F.2 恢复流程
```bash
# 恢复网站文件
tar -xzf /www/backup/web3/web3_files_YYYYMMDD_HHMMSS.tar.gz -C /

# 恢复数据库
mysql -u web3_user -p web3_wallet_db < /www/backup/web3/web3_db_YYYYMMDD_HHMMSS.sql

# 重启服务
bt nginx restart
bt php restart
```

### G. 常见问题FAQ

#### Q1: 钱包连接后显示"网络不支持"
**A1:** 检查以下几点：
1. 确认网站使用HTTPS
2. 检查MetaMask网络设置
3. 验证MultiChainService配置
4. 查看浏览器控制台错误

#### Q2: 监控告警邮件发送失败
**A2:** 排查步骤：
1. 检查SMTP配置是否正确
2. 测试邮箱服务器连通性
3. 查看PHP错误日志
4. 验证邮箱密码和权限

#### Q3: 定时任务不执行
**A3:** 解决方法：
1. 检查crontab配置
2. 验证PHP CLI路径
3. 查看定时任务日志
4. 手动执行测试

#### Q4: 网站访问速度慢
**A4:** 优化建议：
1. 开启Nginx gzip压缩
2. 配置静态资源缓存
3. 优化数据库查询
4. 使用CDN加速

### H. 联系支持

如果在部署过程中遇到问题，可以通过以下方式获取帮助：

1. **查看日志文件**：详细的错误信息通常在日志中
2. **宝塔论坛**：https://www.bt.cn/bbs/
3. **ThinkPHP文档**：https://www.thinkphp.cn/
4. **Web3开发文档**：https://docs.ethers.io/

---

**部署完成后，请务必进行全面的功能测试，确保所有功能正常运行！**
