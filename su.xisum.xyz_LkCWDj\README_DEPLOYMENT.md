# Web3钱包登录系统 - 完整部署指南

## 🎯 项目概述

这是一个基于ThinkPHP 5.1框架开发的Web3钱包登录系统，支持多链网络和完整的监控告警功能。

### 🌟 核心功能
- **Web3钱包登录**：支持MetaMask等主流钱包
- **多链支持**：Ethereum、BSC、Polygon、Arbitrum、Optimism
- **监控告警**：实时监控、异常检测、多种通知方式
- **安全防护**：防重放攻击、签名验证、行为分析
- **管理后台**：完整的用户管理和监控面板

## 📚 部署文档

### 🛠️ 宝塔面板部署（推荐）
- **主要文档**：[BAOTA_DEPLOYMENT_GUIDE.md](./BAOTA_DEPLOYMENT_GUIDE.md)
- **自动部署脚本**：[deploy_baota.sh](./deploy_baota.sh)
- **状态检查脚本**：[check_baota_status.sh](./check_baota_status.sh)

### 📖 其他部署文档
- **基础部署指南**：[WEB3_DEPLOYMENT_GUIDE.md](./WEB3_DEPLOYMENT_GUIDE.md)
- **多链监控指南**：[MULTICHAIN_MONITORING_GUIDE.md](./MULTICHAIN_MONITORING_GUIDE.md)
- **任务记录文档**：[issues/Web3钱包登录功能.md](./issues/Web3钱包登录功能.md)

## 🚀 快速开始

### 方法一：自动部署（推荐）

1. **准备服务器**
   ```bash
   # 安装宝塔面板
   wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh
   ```

2. **上传项目文件**
   ```bash
   # 上传整个项目到服务器
   scp -r ./su.xisum.xyz_LkCWDj root@your-server:/root/
   ```

3. **执行自动部署**
   ```bash
   cd /root/su.xisum.xyz_LkCWDj
   chmod +x deploy_baota.sh
   ./deploy_baota.sh
   ```

4. **按提示输入配置信息**
   - 域名
   - 数据库信息
   - 管理员邮箱
   - SSL配置

### 方法二：手动部署

详细步骤请参考 [BAOTA_DEPLOYMENT_GUIDE.md](./BAOTA_DEPLOYMENT_GUIDE.md)

## 🔧 环境要求

### 服务器配置
- **操作系统**：CentOS 7.0+ / Ubuntu 18.04+ / Debian 9.0+
- **内存**：最低2GB，推荐4GB+
- **硬盘**：最低20GB，推荐50GB+
- **CPU**：最低2核，推荐4核+

### 软件环境
- **宝塔面板**：7.7.0+
- **PHP**：7.4+ 或 8.0+
- **MySQL**：5.7+ 或 8.0+
- **Nginx**：1.18+
- **Redis**：6.0+（可选）

## 📁 项目结构

```
su.xisum.xyz_LkCWDj/
├── application/                    # 应用目录
│   ├── admin/                     # 管理模块
│   │   ├── controller/            # 控制器
│   │   │   ├── Web3Auth.php       # Web3认证控制器
│   │   │   ├── Web3Monitor.php    # 监控管理控制器
│   │   │   ├── Web3MonitorApi.php # 监控API控制器
│   │   │   └── Web3User.php       # Web3用户管理
│   │   ├── command/               # 命令行工具
│   │   │   ├── CleanExpiredNonces.php # 清理过期nonce
│   │   │   └── Web3Monitor.php    # 监控定时任务
│   │   └── view/                  # 视图模板
│   │       └── auth/index.html    # 登录页面（已更新）
│   ├── common/                    # 公共模块
│   │   ├── library/               # 工具库
│   │   │   └── Web3Signature.php  # 签名验证工具
│   │   ├── model/                 # 数据模型
│   │   │   └── User.php           # 用户模型（已扩展）
│   │   └── service/               # 服务类
│   │       ├── MultiChainService.php    # 多链支持服务
│   │       ├── Web3MonitorService.php   # 监控服务
│   │       └── Web3Service.php          # Web3服务（已更新）
│   └── extra/                     # 扩展配置
│       └── web3_monitor.php       # 监控配置文件
├── public/                        # 公共资源
│   └── static/admin/js/
│       └── multi-chain-web3.js    # 多链前端库
├── route/                         # 路由配置
│   └── route.php                  # 路由文件（已更新）
├── web3_migration.sql             # 数据库迁移脚本
├── deploy_baota.sh                # 宝塔自动部署脚本
├── check_baota_status.sh          # 状态检查脚本
└── 部署文档/                      # 部署文档
    ├── BAOTA_DEPLOYMENT_GUIDE.md  # 宝塔部署指南
    ├── WEB3_DEPLOYMENT_GUIDE.md   # 基础部署指南
    └── MULTICHAIN_MONITORING_GUIDE.md # 多链监控指南
```

## 🔍 功能验证

### 部署后检查

1. **运行状态检查**
   ```bash
   ./check_baota_status.sh your-domain.com
   ```

2. **功能测试**
   - 访问网站首页：`https://your-domain.com`
   - 测试钱包连接：点击"连接钱包登录"
   - 访问管理后台：`https://your-domain.com/admin`
   - 查看监控面板：`https://your-domain.com/admin/web3monitor/dashboard`

3. **多链测试**
   - 测试以太坊网络登录
   - 测试BSC网络切换
   - 测试Polygon网络登录

## 📊 监控配置

### 告警通知配置

编辑 `application/extra/web3_monitor.php`：

```php
'notifications' => [
    'email' => [
        'enabled' => true,
        'smtp_host' => 'smtp.your-domain.com',
        'smtp_username' => '<EMAIL>',
        'smtp_password' => 'your_password',
        'to_emails' => ['<EMAIL>']
    ]
]
```

### 定时任务配置

```bash
# Web3监控检查（每5分钟）
*/5 * * * * cd /www/wwwroot/your-domain.com && php think web3:monitor --send-alerts

# 清理过期数据（每天凌晨2点）
0 2 * * * cd /www/wwwroot/your-domain.com && php think clean:nonces
```

## 🛡️ 安全配置

### 必要的安全设置

1. **HTTPS配置**：Web3钱包连接必须使用HTTPS
2. **防火墙设置**：只开放必要端口（80, 443, 22, 8888）
3. **文件权限**：设置正确的文件和目录权限
4. **数据库安全**：使用强密码，限制访问权限

### 安全检查清单

- [ ] SSL证书配置正确
- [ ] 防火墙规则设置完成
- [ ] 敏感文件权限设置正确
- [ ] 数据库密码强度足够
- [ ] 定时备份配置完成
- [ ] 监控告警正常工作

## 🔧 故障排除

### 常见问题

1. **钱包连接失败**
   - 检查是否使用HTTPS
   - 确认MetaMask已安装
   - 查看浏览器控制台错误

2. **网络切换失败**
   - 检查网络配置是否正确
   - 确认钱包支持目标网络
   - 查看前端错误日志

3. **监控告警不工作**
   - 检查定时任务是否运行
   - 验证邮件配置是否正确
   - 查看监控日志文件

### 日志文件位置

```bash
# 应用日志
/www/wwwroot/your-domain.com/runtime/log/

# Web服务器日志
/www/wwwroot/your-domain.com/log/

# 系统日志
/var/log/
```

## 📞 技术支持

### 获取帮助

1. **查看文档**：详细阅读相关部署文档
2. **检查日志**：查看错误日志获取详细信息
3. **运行检查脚本**：使用状态检查脚本诊断问题
4. **社区支持**：
   - 宝塔论坛：https://www.bt.cn/bbs/
   - ThinkPHP文档：https://www.thinkphp.cn/
   - Web3开发文档：https://docs.ethers.io/

### 联系方式

如需技术支持，请提供以下信息：
- 服务器环境信息
- 错误日志内容
- 具体的错误现象
- 已尝试的解决方法

## 📝 更新日志

### v2.0.0 (2025-06-16)
- ✅ 完整的Web3钱包登录系统
- ✅ 多链支持（5个主流网络）
- ✅ 监控告警系统
- ✅ 宝塔面板部署支持
- ✅ 自动化部署脚本
- ✅ 完整的部署文档

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

---

**部署完成后，请务必进行全面的功能测试，确保所有功能正常运行！**

如有任何问题，请参考相应的部署文档或联系技术支持。
