#!/bin/bash

# Web3钱包登录系统 - 宝塔面板状态检查脚本
# 使用方法: chmod +x check_baota_status.sh && ./check_baota_status.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 获取域名参数
DOMAIN=${1:-""}
if [ -z "$DOMAIN" ]; then
    read -p "请输入要检查的域名: " DOMAIN
fi

SITE_DIR="/www/wwwroot/$DOMAIN"

echo "================================================"
echo "Web3钱包登录系统状态检查"
echo "域名: $DOMAIN"
echo "================================================"

# 检查宝塔面板状态
check_baota_panel() {
    echo
    log_info "检查宝塔面板状态..."
    
    if command -v bt &> /dev/null; then
        if bt status | grep -q "running"; then
            log_success "宝塔面板运行正常"
        else
            log_error "宝塔面板未运行"
        fi
    else
        log_error "宝塔面板未安装"
    fi
}

# 检查服务状态
check_services() {
    echo
    log_info "检查服务状态..."
    
    # 检查Nginx
    if bt nginx status | grep -q "running"; then
        log_success "Nginx 运行正常"
    else
        log_error "Nginx 未运行"
    fi
    
    # 检查PHP
    if bt php status | grep -q "running"; then
        log_success "PHP 运行正常"
        
        # 检查PHP版本
        PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
        log_info "PHP版本: $PHP_VERSION"
        
        if [[ $(echo "$PHP_VERSION >= 7.4" | bc -l) -eq 1 ]]; then
            log_success "PHP版本符合要求"
        else
            log_warning "PHP版本过低，建议升级到7.4+"
        fi
    else
        log_error "PHP 未运行"
    fi
    
    # 检查MySQL
    if bt mysql status | grep -q "running"; then
        log_success "MySQL 运行正常"
        
        # 检查MySQL版本
        MYSQL_VERSION=$(mysql --version | awk '{print $5}' | cut -d',' -f1)
        log_info "MySQL版本: $MYSQL_VERSION"
    else
        log_error "MySQL 未运行"
    fi
}

# 检查网站目录
check_site_directory() {
    echo
    log_info "检查网站目录..."
    
    if [ -d "$SITE_DIR" ]; then
        log_success "网站目录存在: $SITE_DIR"
        
        # 检查关键文件
        if [ -f "$SITE_DIR/public/index.php" ]; then
            log_success "入口文件存在"
        else
            log_error "入口文件不存在"
        fi
        
        if [ -d "$SITE_DIR/application" ]; then
            log_success "应用目录存在"
        else
            log_error "应用目录不存在"
        fi
        
        if [ -d "$SITE_DIR/runtime" ]; then
            log_success "运行时目录存在"
            
            # 检查权限
            if [ -w "$SITE_DIR/runtime" ]; then
                log_success "运行时目录可写"
            else
                log_error "运行时目录不可写"
            fi
        else
            log_error "运行时目录不存在"
        fi
        
    else
        log_error "网站目录不存在: $SITE_DIR"
    fi
}

# 检查数据库连接
check_database() {
    echo
    log_info "检查数据库连接..."
    
    if [ -f "$SITE_DIR/application/database.php" ]; then
        log_success "数据库配置文件存在"
        
        # 提取数据库配置
        DB_NAME=$(grep "'database'" "$SITE_DIR/application/database.php" | cut -d"'" -f4)
        DB_USER=$(grep "'username'" "$SITE_DIR/application/database.php" | cut -d"'" -f4)
        
        if [ -n "$DB_NAME" ] && [ -n "$DB_USER" ]; then
            log_info "数据库名: $DB_NAME"
            log_info "数据库用户: $DB_USER"
            
            # 测试数据库连接（需要密码）
            log_info "请手动测试数据库连接"
        else
            log_warning "无法读取数据库配置"
        fi
    else
        log_error "数据库配置文件不存在"
    fi
}

# 检查Web3功能
check_web3_features() {
    echo
    log_info "检查Web3功能..."
    
    # 检查Web3控制器
    if [ -f "$SITE_DIR/application/admin/controller/Web3Auth.php" ]; then
        log_success "Web3认证控制器存在"
    else
        log_error "Web3认证控制器不存在"
    fi
    
    # 检查多链服务
    if [ -f "$SITE_DIR/application/common/service/MultiChainService.php" ]; then
        log_success "多链服务存在"
    else
        log_error "多链服务不存在"
    fi
    
    # 检查监控服务
    if [ -f "$SITE_DIR/application/common/service/Web3MonitorService.php" ]; then
        log_success "监控服务存在"
    else
        log_error "监控服务不存在"
    fi
    
    # 检查前端库
    if [ -f "$SITE_DIR/public/static/admin/js/multi-chain-web3.js" ]; then
        log_success "多链前端库存在"
    else
        log_error "多链前端库不存在"
    fi
}

# 检查SSL证书
check_ssl() {
    echo
    log_info "检查SSL证书..."
    
    if command -v openssl &> /dev/null; then
        SSL_INFO=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            log_success "SSL证书有效"
            
            # 检查证书过期时间
            EXPIRE_DATE=$(echo "$SSL_INFO" | grep "notAfter" | cut -d'=' -f2)
            log_info "证书过期时间: $EXPIRE_DATE"
        else
            log_warning "SSL证书无效或未配置"
        fi
    else
        log_warning "无法检查SSL证书（openssl未安装）"
    fi
}

# 检查网站访问
check_website_access() {
    echo
    log_info "检查网站访问..."
    
    # 检查HTTP访问
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "http://$DOMAIN" 2>/dev/null)
    if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "301" ] || [ "$HTTP_CODE" = "302" ]; then
        log_success "HTTP访问正常 (状态码: $HTTP_CODE)"
    else
        log_error "HTTP访问异常 (状态码: $HTTP_CODE)"
    fi
    
    # 检查HTTPS访问
    HTTPS_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN" 2>/dev/null)
    if [ "$HTTPS_CODE" = "200" ] || [ "$HTTPS_CODE" = "301" ] || [ "$HTTPS_CODE" = "302" ]; then
        log_success "HTTPS访问正常 (状态码: $HTTPS_CODE)"
    else
        log_warning "HTTPS访问异常 (状态码: $HTTPS_CODE)"
    fi
}

# 检查定时任务
check_cron_jobs() {
    echo
    log_info "检查定时任务..."
    
    CRON_LIST=$(crontab -l 2>/dev/null)
    
    if echo "$CRON_LIST" | grep -q "web3:monitor"; then
        log_success "Web3监控定时任务已配置"
    else
        log_warning "Web3监控定时任务未配置"
    fi
    
    if echo "$CRON_LIST" | grep -q "clean:nonces"; then
        log_success "清理nonce定时任务已配置"
    else
        log_warning "清理nonce定时任务未配置"
    fi
}

# 检查日志文件
check_logs() {
    echo
    log_info "检查日志文件..."
    
    # 检查访问日志
    if [ -f "$SITE_DIR/log/access.log" ]; then
        log_success "访问日志存在"
        
        # 显示最近的访问记录
        RECENT_ACCESS=$(tail -n 5 "$SITE_DIR/log/access.log" 2>/dev/null | wc -l)
        log_info "最近访问记录: $RECENT_ACCESS 条"
    else
        log_warning "访问日志不存在"
    fi
    
    # 检查错误日志
    if [ -f "$SITE_DIR/log/error.log" ]; then
        log_success "错误日志存在"
        
        # 检查是否有错误
        ERROR_COUNT=$(wc -l < "$SITE_DIR/log/error.log" 2>/dev/null || echo "0")
        if [ "$ERROR_COUNT" -gt 0 ]; then
            log_warning "发现 $ERROR_COUNT 条错误记录"
        else
            log_success "无错误记录"
        fi
    else
        log_warning "错误日志不存在"
    fi
    
    # 检查应用日志
    if [ -d "$SITE_DIR/runtime/log" ]; then
        log_success "应用日志目录存在"
        
        LOG_FILES=$(find "$SITE_DIR/runtime/log" -name "*.log" | wc -l)
        log_info "日志文件数量: $LOG_FILES"
    else
        log_warning "应用日志目录不存在"
    fi
}

# 检查系统资源
check_system_resources() {
    echo
    log_info "检查系统资源..."
    
    # 检查磁盘使用率
    DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -lt 80 ]; then
        log_success "磁盘使用率正常: ${DISK_USAGE}%"
    elif [ "$DISK_USAGE" -lt 90 ]; then
        log_warning "磁盘使用率较高: ${DISK_USAGE}%"
    else
        log_error "磁盘使用率过高: ${DISK_USAGE}%"
    fi
    
    # 检查内存使用率
    MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$MEMORY_USAGE" -lt 80 ]; then
        log_success "内存使用率正常: ${MEMORY_USAGE}%"
    elif [ "$MEMORY_USAGE" -lt 90 ]; then
        log_warning "内存使用率较高: ${MEMORY_USAGE}%"
    else
        log_error "内存使用率过高: ${MEMORY_USAGE}%"
    fi
    
    # 检查CPU负载
    LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    log_info "系统负载: $LOAD_AVG"
}

# 生成检查报告
generate_report() {
    echo
    echo "================================================"
    echo "检查完成！"
    echo "================================================"
    echo
    echo "建议检查项目:"
    echo "1. 如果SSL证书未配置，请在宝塔面板中申请"
    echo "2. 如果定时任务未配置，请手动添加"
    echo "3. 如果有错误日志，请查看具体错误信息"
    echo "4. 定期监控系统资源使用情况"
    echo
    echo "重要文件位置:"
    echo "- 网站目录: $SITE_DIR"
    echo "- 配置文件: $SITE_DIR/application/database.php"
    echo "- 监控配置: $SITE_DIR/application/extra/web3_monitor.php"
    echo "- 日志目录: $SITE_DIR/runtime/log/"
    echo
    echo "管理地址:"
    echo "- 网站首页: https://$DOMAIN"
    echo "- 管理后台: https://$DOMAIN/admin"
    echo "- 监控面板: https://$DOMAIN/admin/web3monitor/dashboard"
    echo
}

# 主函数
main() {
    check_baota_panel
    check_services
    check_site_directory
    check_database
    check_web3_features
    check_ssl
    check_website_access
    check_cron_jobs
    check_logs
    check_system_resources
    generate_report
}

# 执行主函数
main
